param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING PARAMETER TABLES FOR: $CommandName ===" -ForegroundColor Green
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        Write-Host "Parameters section found. Length: $($parametersSection.Length)" -ForegroundColor Yellow
        
        # Find the first parameter to examine its structure
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)')
        
        if ($paramMatches.Count -gt 0) {
            $firstParam = $paramMatches[0]
            $paramName = $firstParam.Groups[1].Value.Trim()
            $paramContent = $firstParam.Groups[2].Value
            
            Write-Host "`n=== FIRST PARAMETER: $paramName ===" -ForegroundColor Cyan
            Write-Host "Content length: $($paramContent.Length)" -ForegroundColor White
            
            # Look for tables in this parameter
            $tableMatches = [regex]::Matches($paramContent, '(?s)<table[^>]*>(.*?)</table>')
            Write-Host "Tables found: $($tableMatches.Count)" -ForegroundColor White
            
            for ($i = 0; $i -lt $tableMatches.Count; $i++) {
                Write-Host "`n--- TABLE $($i + 1) ---" -ForegroundColor Magenta
                $tableContent = $tableMatches[$i].Groups[1].Value
                
                # Extract rows
                $rowMatches = [regex]::Matches($tableContent, '(?s)<tr[^>]*>(.*?)</tr>')
                Write-Host "Rows in table: $($rowMatches.Count)" -ForegroundColor White
                
                foreach ($rowMatch in $rowMatches) {
                    $rowContent = $rowMatch.Groups[1].Value
                    $cellMatches = [regex]::Matches($rowContent, '<td[^>]*>(.*?)</td>')
                    
                    if ($cellMatches.Count -ge 2) {
                        $key = $cellMatches[0].Groups[1].Value -replace '<[^>]+>', ''
                        $value = $cellMatches[1].Groups[1].Value -replace '<[^>]+>', ''
                        Write-Host "  $($key.Trim()) = $($value.Trim())" -ForegroundColor Gray
                    }
                }
            }
            
            # Also check if there are any divs or other structures
            Write-Host "`n--- RAW PARAMETER CONTENT (first 500 chars) ---" -ForegroundColor Yellow
            Write-Host $paramContent.Substring(0, [Math]::Min(500, $paramContent.Length)) -ForegroundColor Gray
        }
    } else {
        Write-Host "No Parameters section found!" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
