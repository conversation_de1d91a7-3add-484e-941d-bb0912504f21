Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DEBUGGING PARAMETER PROPERTIES TABLES ==="

# Find all parameter headers
$paramHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3[following-sibling::h2[1][text()='Inputs' or text()='Outputs' or text()='Notes' or text()='Related Links']]")

Write-Host "Found $($paramHeaders.Count) parameter headers"

# Focus on first parameter to understand table structure
if ($paramHeaders.Count -gt 0) {
    $firstParam = $paramHeaders[0]
    Write-Host "`nAnalyzing parameter: $($firstParam.InnerText)"
    
    # Get all tables that come after this parameter header
    $allTablesAfterParam = $htmlDocument.SelectNodes("//h3[text()='$($firstParam.InnerText)']/following-sibling::table")
    Write-Host "Found $($allTablesAfterParam.Count) tables after this parameter"
    
    foreach ($table in $allTablesAfterParam) {
        Write-Host "`n--- TABLE ---"
        $rows = $table.SelectNodes(".//tr")
        if ($rows) {
            foreach ($row in $rows) {
                $cells = $row.SelectNodes(".//td")
                if ($cells -and $cells.Count -ge 2) {
                    Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                }
            }
        }
    }
    
    # Also check what comes immediately after the parameter header
    Write-Host "`n--- IMMEDIATE CONTENT AFTER PARAMETER HEADER ---"
    $nextNode = $firstParam.NextSibling
    $nodeCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3') -and $nodeCount -lt 20) {
        if ($nextNode.Name -eq 'table') {
            Write-Host "`nTABLE $nodeCount :"
            $rows = $nextNode.SelectNodes(".//tr")
            if ($rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($cells -and $cells.Count -ge 2) {
                        Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                    }
                }
            }
        }
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
}

# Let's also check if there are any tables that contain "Type" information
Write-Host "`n=== SEARCHING FOR TABLES WITH TYPE INFORMATION ==="
$allTables = $htmlDocument.SelectNodes("//table")
$typeTableCount = 0

foreach ($table in $allTables) {
    $hasType = $false
    $rows = $table.SelectNodes(".//tr")
    if ($rows) {
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                $key = $cells[0].InnerText.Trim()
                if ($key -match "Type") {
                    $hasType = $true
                    break
                }
            }
        }
    }
    
    if ($hasType) {
        $typeTableCount++
        Write-Host "`n--- TYPE TABLE $typeTableCount ---"
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
            }
        }
    }
}

Write-Host "`nFound $typeTableCount tables with Type information"
