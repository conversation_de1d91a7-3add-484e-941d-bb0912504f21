# Debug description extraction with the new approach
$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember"

try {
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING FIXED DESCRIPTION EXTRACTION ===" -ForegroundColor Yellow
    
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        
        # Find each example section (h3 + div pairs)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>(.*?)</h3>\s*<div[^>]*>(.*?)</div>')
        
        Write-Host "Found $($exampleMatches.Count) example sections" -ForegroundColor Green
        
        for ($i = 0; $i -lt $exampleMatches.Count; $i++) {
            $title = $exampleMatches[$i].Groups[1].Value -replace '<[^>]+>', ''
            $exampleContent = $exampleMatches[$i].Groups[2].Value
            
            Write-Host "`n--- Example $($i + 1): $title ---" -ForegroundColor Cyan
            
            # Remove the pre/code block first, then extract p tags
            $contentAfterCode = $exampleContent -replace '(?s)<pre><code[^>]*>.*?</code></pre>', ''
            Write-Host "Content after removing code block (length: $($contentAfterCode.Length)):" -ForegroundColor Yellow
            Write-Host $contentAfterCode.Substring(0, [Math]::Min(300, $contentAfterCode.Length)) -ForegroundColor White
            
            $descriptionMatches = [regex]::Matches($contentAfterCode, '(?s)<p[^>]*>(.*?)</p>')
            Write-Host "Found $($descriptionMatches.Count) paragraph(s) after code removal" -ForegroundColor Green
            
            foreach ($descMatch in $descriptionMatches) {
                $descText = $descMatch.Groups[1].Value
                Write-Host "  Raw p content: $($descText.Substring(0, [Math]::Min(200, $descText.Length)))..." -ForegroundColor Cyan
                
                # Clean up the text
                $cleanText = $descText -replace '<code[^>]*>(.*?)</code>', '$1' # Keep code content but remove tags
                $cleanText = $cleanText -replace '<[^>]+>', '' # Remove other HTML tags
                $cleanText = $cleanText.Trim()
                
                Write-Host "  Cleaned text: $cleanText" -ForegroundColor Green
                Write-Host "  Length: $($cleanText.Length)" -ForegroundColor White
            }
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
