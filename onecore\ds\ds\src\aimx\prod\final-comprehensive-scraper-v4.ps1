param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content using Invoke-WebRequest
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    # --- Extract Syntax ---
    $syntax = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)<h2') {
        $syntaxSection = $matches[1]
        
        # Find all pre/code blocks in syntax section
        $syntaxMatches = [regex]::Matches($syntaxSection, '(?s)<pre><code[^>]*>(.*?)</code></pre>')
        foreach ($match in $syntaxMatches) {
            $syntaxText = $match.Groups[1].Value
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            $syntaxText = $syntaxText -replace '<[^>]+>', '' # Remove any remaining HTML tags
            $syntaxText = $syntaxText.Trim()
            
            if ($syntaxText -and $syntaxText.Length -gt 10) {
                $syntax += $syntaxText
            }
        }
    }
    
    # --- Extract Parameters (simplified approach using regex) ---
    $parameters = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        # Find parameter headers (h3 tags) - handle HTML tags within parameter names
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>(.*?)</h3>(.*?)(?=<h3|$)')

        foreach ($match in $paramMatches) {
            # Extract parameter name and remove HTML tags (like <wbr>)
            $paramNameRaw = $match.Groups[1].Value
            $paramName = ($paramNameRaw -replace '<[^>]+>', '').Trim() -replace '^-', ''
            $paramContent = $match.Groups[2].Value

            # Handle CommonParameters section specially
            if ($paramName -eq "CommonParameters") {
                # Extract common parameters information
                if ($paramContent -match '(?s)<p[^>]*>(.*?)</p>') {
                    $commonParamsText = $matches[1] -replace '<[^>]+>', ''
                    $commonParamsText = $commonParamsText.Trim()

                    # Add CommonParameters as a special parameter entry
                    $commonParam = [PSCustomObject]@{
                        Name = "CommonParameters"
                        Description = $commonParamsText
                        Details = [PSCustomObject]@{
                            Type = "CommonParameters"
                            Position = "Named"
                            Default = ""
                            Required = "False"
                            AcceptPipelineInput = "False"
                            AcceptWildcardCharacters = "False"
                            Aliases = ""
                        }
                    }
                    $parameters += $commonParam
                }
                continue
            }

            # Extract description from first paragraph
            $description = ""
            if ($paramContent -match '<p[^>]*>(.*?)</p>') {
                $description = $matches[1] -replace '<[^>]+>', '' # Remove HTML tags
                $description = $description.Trim()
                
                # Also check for UL lists with additional values
                $ulMatches = [regex]::Matches($paramContent, '(?s)<ul[^>]*>(.*?)</ul>')
                foreach ($ulMatch in $ulMatches) {
                    $listContent = $ulMatch.Groups[1].Value
                    $listItems = [regex]::Matches($listContent, '<li[^>]*>(.*?)</li>')
                    $listText = ($listItems | ForEach-Object { $_.Groups[1].Value -replace '<[^>]+>', '' }) -join ", "
                    if ($listText) {
                        $description += " " + $listText.Trim()
                    }
                }
            }
            
            # Extract parameter details from tables
            $paramDetails = @{
                Type = ""
                Position = ""
                Default = ""
                Required = ""
                AcceptPipelineInput = ""
                AcceptWildcardCharacters = ""
                Aliases = ""
            }

            # Extract from Parameter Properties table (Type, Default value, etc.)
            if ($paramContent -match '(?s)<h4[^>]*>Parameter properties</h4>.*?<table[^>]*>(.*?)</table>') {
                $propertiesTable = $matches[1]
                $rowMatches = [regex]::Matches($propertiesTable, '(?s)<tr[^>]*>(.*?)</tr>')

                foreach ($rowMatch in $rowMatches) {
                    $rowContent = $rowMatch.Groups[1].Value
                    $cellMatches = [regex]::Matches($rowContent, '(?s)<td[^>]*>(.*?)</td>')

                    if ($cellMatches.Count -ge 2) {
                        $key = $cellMatches[0].Groups[1].Value -replace '<[^>]+>', ''
                        $value = $cellMatches[1].Groups[1].Value -replace '<[^>]+>', ''
                        $key = $key.Trim()
                        $value = $value.Trim()

                        switch ($key) {
                            "Type:" { $paramDetails.Type = $value }
                            "Default value:" { $paramDetails.Default = $value }
                            "Supports wildcards:" { $paramDetails.AcceptWildcardCharacters = $value }
                            "Aliases:" { $paramDetails.Aliases = $value }
                        }
                    }
                }
            }

            # Extract from Parameter Sets table (Position, Mandatory, etc.)
            if ($paramContent -match '(?s)<h4[^>]*>Parameter sets</h4>.*?<table[^>]*>(.*?)</table>') {
                $setsTable = $matches[1]
                $rowMatches = [regex]::Matches($setsTable, '(?s)<tr[^>]*>(.*?)</tr>')

                foreach ($rowMatch in $rowMatches) {
                    $rowContent = $rowMatch.Groups[1].Value
                    $cellMatches = [regex]::Matches($rowContent, '(?s)<td[^>]*>(.*?)</td>')

                    if ($cellMatches.Count -ge 2) {
                        $key = $cellMatches[0].Groups[1].Value -replace '<[^>]+>', ''
                        $value = $cellMatches[1].Groups[1].Value -replace '<[^>]+>', ''
                        $key = $key.Trim()
                        $value = $value.Trim()

                        switch ($key) {
                            "Position:" { $paramDetails.Position = $value }
                            "Mandatory:" { $paramDetails.Required = $value }
                            "Value from pipeline:" { $paramDetails.AcceptPipelineInput = $value }
                        }
                    }
                }
            }
            
            $parameters += [PSCustomObject]@{
                Name = $paramName
                Description = $description
                Details = $paramDetails
            }
        }
    }
    
    # --- Extract Examples with Descriptions ---
    $examples = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        
        # Find each example section (h3 + div pairs)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>.*?</h3>\s*<div[^>]*>(.*?)</div>')
        
        foreach ($match in $exampleMatches) {
            $exampleContent = $match.Groups[1].Value
            
            # Extract code from pre/code block
            $code = ""
            if ($exampleContent -match '(?s)<pre><code[^>]*>(.*?)</code></pre>') {
                $code = $matches[1]
                $code = $code -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                $code = $code -replace '<[^>]+>', '' # Remove HTML tags
                $code = $code.Trim()
            }
            
            # Extract description from p tags that follow the code
            $description = ""
            # Remove the pre/code block first, then extract p tags
            $contentAfterCode = $exampleContent -replace '(?s)<pre><code[^>]*>.*?</code></pre>', ''
            $descriptionMatches = [regex]::Matches($contentAfterCode, '(?s)<p[^>]*>(.*?)</p>')
            $descriptions = @()
            foreach ($descMatch in $descriptionMatches) {
                $descText = $descMatch.Groups[1].Value
                $descText = $descText -replace '<code[^>]*>(.*?)</code>', '$1' # Keep code content but remove tags
                $descText = $descText -replace '<[^>]+>', '' # Remove other HTML tags
                $descText = $descText.Trim()
                
                if ($descText -and $descText.Length -gt 10) {
                    $descriptions += $descText
                }
            }
            
            if ($descriptions.Count -gt 0) {
                $description = $descriptions -join " "
            }
            
            if ($code) {
                $examples += [PSCustomObject]@{
                    Code = $code
                    Description = $description
                }
            }
        }
    }
    
    # --- Create final result ---
    $result = [PSCustomObject]@{
        CommandName = $CommandName
        Syntax = $syntax
        Parameters = $parameters
        Examples = $examples
    }
    
    # Output as JSON
    $result | ConvertTo-Json -Depth 10
}
catch {
    Write-Error "Error processing $CommandName`: $($_.Exception.Message)"
    # Return empty result on error
    [PSCustomObject]@{
        CommandName = $CommandName
        Syntax = @()
        Parameters = @()
        Examples = @()
        Error = $_.Exception.Message
    } | ConvertTo-Json -Depth 10
}
