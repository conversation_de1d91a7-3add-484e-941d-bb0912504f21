Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DETAILED HTML STRUCTURE ANALYSIS ==="

# 1. Check parameter structure more thoroughly
Write-Host "`n--- PARAMETER STRUCTURE ANALYSIS ---"
$authTypeHeader = $htmlDocument.SelectSingleNode("//h3[contains(text(), '-AuthType')]")
if ($authTypeHeader) {
    Write-Host "Found -AuthType header: $($authTypeHeader.InnerText)"
    
    # Walk through ALL siblings to understand the complete structure
    $nextNode = $authTypeHeader.NextSibling
    $nodeCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3') -and $nodeCount -lt 30) {
        Write-Host "`nNode $nodeCount : $($nextNode.Name)"
        
        if ($nextNode.Name -eq 'p') {
            $text = $nextNode.InnerText.Trim()
            if ($text.Length -gt 0) {
                Write-Host "  P: $($text.Substring(0, [Math]::Min(150, $text.Length)))..."
            }
        }
        
        if ($nextNode.Name -eq 'ul') {
            Write-Host "  UL found - checking list items:"
            $listItems = $nextNode.SelectNodes(".//li")
            if ($listItems) {
                foreach ($li in $listItems) {
                    Write-Host "    LI: $($li.InnerText.Trim())"
                }
            }
        }
        
        if ($nextNode.Name -eq 'table') {
            Write-Host "  TABLE found:"
            $rows = $nextNode.SelectNodes(".//tr")
            if ($rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($cells -and $cells.Count -ge 2) {
                        Write-Host "    $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                    }
                }
            }
        }
        
        if ($nextNode.Name -eq 'h4') {
            Write-Host "  H4: $($nextNode.InnerText)"
        }
        
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
}

# 2. Check example structure more thoroughly
Write-Host "`n--- EXAMPLE STRUCTURE ANALYSIS ---"
$exampleHeaders = $htmlDocument.SelectNodes("//h2[text()='Examples']/following-sibling::h3")
if ($exampleHeaders) {
    Write-Host "Found $($exampleHeaders.Count) example headers"
    
    # Check first example in detail
    $firstExample = $exampleHeaders[0]
    Write-Host "First example header: $($firstExample.InnerText)"
    
    $nextNode = $firstExample.NextSibling
    $nodeCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3') -and $nodeCount -lt 15) {
        Write-Host "`nExample Node $nodeCount : $($nextNode.Name)"
        
        if ($nextNode.Name -eq 'p') {
            $text = $nextNode.InnerText.Trim()
            if ($text.Length -gt 0) {
                Write-Host "  P: $($text.Substring(0, [Math]::Min(200, $text.Length)))..."
            }
        }
        
        if ($nextNode.Name -eq 'div') {
            Write-Host "  DIV found - checking for code:"
            $codeNodes = $nextNode.SelectNodes(".//code")
            if ($codeNodes) {
                foreach ($code in $codeNodes) {
                    Write-Host "    CODE: $($code.InnerText.Trim())"
                }
            }
        }
        
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
}

# 3. Look for Syntax section
Write-Host "`n--- SYNTAX SECTION ANALYSIS ---"
$syntaxHeader = $htmlDocument.SelectSingleNode("//h2[text()='Syntax']")
if ($syntaxHeader) {
    Write-Host "Found Syntax header"
    
    $nextNode = $syntaxHeader.NextSibling
    $nodeCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -ne 'h2' -and $nodeCount -lt 10) {
        Write-Host "`nSyntax Node $nodeCount : $($nextNode.Name)"
        
        if ($nextNode.Name -eq 'div') {
            $codeNodes = $nextNode.SelectNodes(".//code")
            if ($codeNodes) {
                foreach ($code in $codeNodes) {
                    Write-Host "  SYNTAX CODE: $($code.InnerText.Trim())"
                }
            }
        }
        
        if ($nextNode.Name -eq 'p') {
            Write-Host "  SYNTAX P: $($nextNode.InnerText.Trim())"
        }
        
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
} else {
    Write-Host "No Syntax header found"
}

# 4. Look for all tables in the entire document
Write-Host "`n--- ALL TABLES ANALYSIS ---"
$allTables = $htmlDocument.SelectNodes("//table")
Write-Host "Found $($allTables.Count) total tables in document"

for ($i = 0; $i -lt [Math]::Min(5, $allTables.Count); $i++) {
    Write-Host "`n--- TABLE $($i + 1) ---"
    $table = $allTables[$i]
    $rows = $table.SelectNodes(".//tr")
    if ($rows) {
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
            }
        }
    }
}
