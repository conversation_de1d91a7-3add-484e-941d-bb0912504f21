# Examine the actual HTML structure for syntax and examples
$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember"

try {
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== EXAMINING SYNTAX SECTION STRUCTURE ===" -ForegroundColor Yellow
    
    # Extract and examine the syntax section
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)<h2') {
        $syntaxSection = $matches[1]
        Write-Host "Syntax section content:" -ForegroundColor Green
        Write-Host $syntaxSection.Substring(0, [Math]::Min(1000, $syntaxSection.Length)) -ForegroundColor White
        Write-Host "..." -ForegroundColor White
    }
    
    Write-Host "`n=== EXAMINING EXAMPLES SECTION STRUCTURE ===" -ForegroundColor Yellow
    
    # Extract and examine the examples section  
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        Write-Host "Examples section content (first 2000 chars):" -ForegroundColor Green
        Write-Host $examplesSection.Substring(0, [Math]::Min(2000, $examplesSection.Length)) -ForegroundColor White
        Write-Host "..." -ForegroundColor White
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
