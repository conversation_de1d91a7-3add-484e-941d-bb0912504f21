Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DEBUGGING ALL PARAMETER INFO ==="

# Get all tables in the parameters section
$allTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")
Write-Host "Found $($allTables.Count) tables in Parameters section"

for ($i = 0; $i -lt [Math]::Min(3, $allTables.Count); $i++) {
    Write-Host "`n--- TABLE $($i + 1) ---"
    $table = $allTables[$i]
    $rows = $table.SelectNodes(".//tr")
    if ($rows) {
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
            }
        }
    }
}

# Also check if there are any other structures that might contain Type, Default value, etc.
Write-Host "`n=== LOOKING FOR TYPE AND OTHER DETAILS ==="

# Check for any text containing "Type:", "Default:", "Aliases:" etc.
$paramSection = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($paramSection) {
    $allText = $paramSection.SelectSingleNode("./following-sibling::*[following-sibling::h2[1][text()='Inputs']]").InnerText
    
    if ($allText -match "Type:") {
        Write-Host "Found 'Type:' in parameters section"
    }
    if ($allText -match "Default:") {
        Write-Host "Found 'Default:' in parameters section"  
    }
    if ($allText -match "Aliases:") {
        Write-Host "Found 'Aliases:' in parameters section"
    }
}

# Check the first parameter (-AuthType) more thoroughly
Write-Host "`n=== DETAILED ANALYSIS OF -AuthType ==="
$authTypeNodes = $htmlDocument.SelectNodes("//h3[text()='-AuthType']/following-sibling::*[following-sibling::h3[1] or following-sibling::h2[1]]")
Write-Host "Found $($authTypeNodes.Count) nodes after -AuthType header"

foreach ($node in $authTypeNodes) {
    if ($node.Name -eq 'p') {
        $text = $node.InnerText.Trim()
        if ($text.Length -gt 0) {
            Write-Host "P: $($text.Substring(0, [Math]::Min(150, $text.Length)))..."
        }
    }
    if ($node.Name -eq 'ul') {
        Write-Host "UL: $($node.InnerText.Trim().Substring(0, [Math]::Min(100, $node.InnerText.Trim().Length)))..."
    }
    if ($node.Name -eq 'table') {
        Write-Host "TABLE found in -AuthType"
    }
}
