Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DEBUGGING HTML STRUCTURE ==="

# Check for synopsis/description patterns
Write-Host "`n--- Looking for Synopsis/Description ---"
$h1 = $htmlDocument.SelectSingleNode("//h1")
Write-Host "H1: $($h1.InnerText)"

# Check what comes after H1
$nextNode = $h1.NextSibling
$count = 0
while ($null -ne $nextNode -and $count -lt 10) {
    if ($nextNode.Name -eq 'p') {
        Write-Host "P after H1: $($nextNode.InnerText.Substring(0, [Math]::Min(100, $nextNode.InnerText.Length)))..."
    }
    $nextNode = $nextNode.NextSibling
    $count++
}

# Check for Description section
$descHeader = $htmlDocument.SelectSingleNode("//h2[text()='Description']")
if ($descHeader) {
    Write-Host "`nFound Description header"
    $nextNode = $descHeader.NextSibling
    $count = 0
    while ($null -ne $nextNode -and $nextNode.Name -ne 'h2' -and $count -lt 5) {
        if ($nextNode.Name -eq 'p') {
            Write-Host "P in Description: $($nextNode.InnerText.Substring(0, [Math]::Min(100, $nextNode.InnerText.Length)))..."
        }
        $nextNode = $nextNode.NextSibling
        $count++
    }
} else {
    Write-Host "No Description header found"
}

# Check parameter structure
Write-Host "`n--- Parameter Structure ---"
$paramHeader = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($paramHeader) {
    Write-Host "Found Parameters header"
    # Look at first parameter
    $firstParam = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']/following-sibling::h3[1]")
    if ($firstParam) {
        Write-Host "First parameter: $($firstParam.InnerText)"
        
        # Check what follows the parameter header
        $nextNode = $firstParam.NextSibling
        $count = 0
        while ($null -ne $nextNode -and $nextNode.Name -ne 'h3' -and $nextNode.Name -ne 'h2' -and $count -lt 10) {
            Write-Host "  Node: $($nextNode.Name) - $($nextNode.InnerText.Substring(0, [Math]::Min(50, $nextNode.InnerText.Length)))..."
            
            # Check for dl/dt/dd structure
            if ($nextNode.Name -eq 'dl' -or $nextNode.SelectNodes(".//dl")) {
                Write-Host "    Found DL structure!"
                $dtNodes = $nextNode.SelectNodes(".//dt")
                if ($dtNodes) {
                    foreach ($dt in $dtNodes) {
                        $dd = $dt.SelectSingleNode("./following-sibling::dd[1]")
                        Write-Host "      $($dt.InnerText): $($dd.InnerText)"
                    }
                }
            }
            
            $nextNode = $nextNode.NextSibling
            $count++
        }
    }
}

# Check example structure
Write-Host "`n--- Example Structure ---"
$exampleHeader = $htmlDocument.SelectSingleNode("//h2[text()='Examples']")
if ($exampleHeader) {
    Write-Host "Found Examples header"
    $firstExample = $htmlDocument.SelectSingleNode("//h2[text()='Examples']/following-sibling::h3[1]")
    if ($firstExample) {
        Write-Host "First example: $($firstExample.InnerText)"
        
        # Check what follows the example header
        $nextNode = $firstExample.NextSibling
        $count = 0
        while ($null -ne $nextNode -and $nextNode.Name -ne 'h3' -and $nextNode.Name -ne 'h2' -and $count -lt 10) {
            Write-Host "  Node: $($nextNode.Name)"
            if ($nextNode.Name -eq 'p') {
                Write-Host "    P: $($nextNode.InnerText.Substring(0, [Math]::Min(100, $nextNode.InnerText.Length)))..."
            }
            if ($nextNode.Name -eq 'div') {
                $codeNodes = $nextNode.SelectNodes(".//code")
                if ($codeNodes) {
                    foreach ($code in $codeNodes) {
                        Write-Host "    CODE: $($code.InnerText)"
                    }
                }
            }
            $nextNode = $nextNode.NextSibling
            $count++
        }
    }
}
