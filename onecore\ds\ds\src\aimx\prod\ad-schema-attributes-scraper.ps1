<#
.SYNOPSIS
    Active Directory Schema Attributes scraper for Microsoft Learn documentation.

.DESCRIPTION
    This script extracts Active Directory schema attribute information from Microsoft Learn documentation,
    including the LDAP Display Name and description for each attribute. The output is a JSON file
    where the key is the LDAP Display Name and the value is the description.

.PARAMETER OutputFile
    The path where the JSON output file should be saved. Default is "ad-schema-attributes.json"

.PARAMETER MaxAttributes
    Maximum number of attributes to process (for testing). If not specified, processes all attributes.

.EXAMPLE
    .\ad-schema-attributes-scraper.ps1 -OutputFile "ad-attributes.json"
    
.EXAMPLE
    .\ad-schema-attributes-scraper.ps1 -MaxAttributes 10 -OutputFile "test-attributes.json"
#>
param (
    [Parameter(Mandatory = $false)]
    [string]$OutputFile = "ad-schema-attributes.json",
    
    [Parameter(Mandatory = $false)]
    [int]$MaxAttributes = 0
)

# Set UTF-8 encoding for output
$PSDefaultParameterValues['Out-File:Encoding'] = 'utf8'

try {
    Import-Module PowerHTML -ErrorAction Stop
}
catch {
    Write-Error "The 'PowerHTML' module is not installed. Please run: Install-Module -Name PowerHTML"
    return
}

# Base URL for AD schema documentation
$baseUrl = "https://learn.microsoft.com/en-us/windows/win32/adschema/"
$allAttributesUrl = $baseUrl + "attributes-all"

Write-Host "Fetching list of all attributes from: $allAttributesUrl"

try {
    $htmlDocument = ConvertFrom-Html -Uri $allAttributesUrl -ErrorAction Stop
}
catch {
    Write-Error "Failed to fetch or parse the attributes list URL: $allAttributesUrl. Please check the URL and your internet connection."
    return
}

# Extract all attribute links from the page
$attributeLinks = $htmlDocument.SelectNodes("//a[starts-with(@href, 'a-')]")

if ($null -eq $attributeLinks -or $attributeLinks.Count -eq 0) {
    Write-Error "No attribute links found on the page. The page structure may have changed."
    return
}

Write-Host "Found $($attributeLinks.Count) attribute links"

# Limit processing if MaxAttributes is specified
$linksToProcess = $attributeLinks
if ($MaxAttributes -gt 0 -and $MaxAttributes -lt $attributeLinks.Count) {
    $linksToProcess = $attributeLinks | Select-Object -First $MaxAttributes
    Write-Host "Processing first $MaxAttributes attributes for testing"
}

$attributeData = @{}
$processedCount = 0
$errorCount = 0

foreach ($link in $linksToProcess) {
    $processedCount++
    $attributeHref = $link.GetAttributeValue("href", "")
    $attributeName = $link.InnerText.Trim()
    
    if ([string]::IsNullOrEmpty($attributeHref)) {
        Write-Warning "Skipping attribute with empty href: $attributeName"
        continue
    }
    
    $attributeUrl = $baseUrl + $attributeHref
    Write-Host "[$processedCount/$($linksToProcess.Count)] Processing: $attributeName"
    Write-Verbose "URL: $attributeUrl"
    
    try {
        $attributeDoc = ConvertFrom-Html -Uri $attributeUrl -ErrorAction Stop
        
        # Extract description - it's the first paragraph after "In this article" section
        $description = ""

        # Look for the "In this article" heading and get the next paragraph
        $inThisArticleNode = $attributeDoc.SelectSingleNode("//h3[contains(text(), 'In this article')]")
        if ($null -ne $inThisArticleNode) {
            # Get the next sibling that is a paragraph
            $nextNode = $inThisArticleNode.NextSibling
            while ($null -ne $nextNode) {
                if ($nextNode.Name -eq 'p' -and -not [string]::IsNullOrWhiteSpace($nextNode.InnerText)) {
                    $candidateText = $nextNode.InnerText.Trim()
                    # Make sure it's not feedback text or other navigation elements
                    if ($candidateText -notmatch "^(Was this page helpful|Feedback|In this article|Share via|Additional resources)" -and
                        $candidateText -notmatch "Yes No$" -and
                        $candidateText.Length -gt 5) {
                        $description = $candidateText
                        break
                    }
                }
                $nextNode = $nextNode.NextSibling
            }
        }

        # If that didn't work, try looking for the first meaningful paragraph after h1 but before the first table
        if ([string]::IsNullOrEmpty($description)) {
            # Look for paragraphs between h1 and the first table (which contains the properties)
            $paragraphs = $attributeDoc.SelectNodes("//h1/following::p[following::table]")
            foreach ($p in $paragraphs) {
                $text = $p.InnerText.Trim()
                # Skip navigation text and look for actual description
                if (-not [string]::IsNullOrWhiteSpace($text) -and
                    $text -notmatch "^(Feedback|In this article|Share via|Was this page helpful|Additional resources)" -and
                    $text -notmatch "Yes No$" -and
                    $text -notmatch "^\d{4}-\d{2}-\d{2}$" -and  # Skip date stamps
                    $text.Length -gt 5 -and $text.Length -lt 500) {  # Reasonable description length
                    $description = $text
                    break
                }
            }
        }
        
        # Extract LDAP Display Name from the table
        $ldapDisplayName = ""
        $ldapRow = $attributeDoc.SelectSingleNode("//td[text()='Ldap-Display-Name']/following-sibling::td[1]")
        if ($null -ne $ldapRow) {
            $ldapDisplayName = $ldapRow.InnerText.Trim()
        }
        
        # Only add to results if we have both pieces of information
        if (-not [string]::IsNullOrEmpty($ldapDisplayName) -and -not [string]::IsNullOrEmpty($description)) {
            $attributeData[$ldapDisplayName] = $description
            Write-Verbose "Added: $ldapDisplayName = $description"
        }
        else {
            Write-Warning "Missing data for $attributeName - LDAP Name: '$ldapDisplayName', Description: '$description'"
            $errorCount++
        }
        
        # Small delay to be respectful to the server
        Start-Sleep -Milliseconds 100
    }
    catch {
        Write-Warning "Failed to process attribute $attributeName at $attributeUrl : $($_.Exception.Message)"
        $errorCount++
        continue
    }
}

Write-Host "`nProcessing complete!"
Write-Host "Successfully processed: $($attributeData.Count) attributes"
Write-Host "Errors encountered: $errorCount"

if ($attributeData.Count -eq 0) {
    Write-Error "No attribute data was successfully extracted. Please check the script and try again."
    return
}

# Convert to JSON and save
try {
    $jsonOutput = $attributeData | ConvertTo-Json -Depth 2
    $jsonOutput | Out-File -FilePath $OutputFile -Force
    Write-Host "Results saved to: $OutputFile"
    Write-Host "Sample entries:"
    $attributeData.GetEnumerator() | Select-Object -First 3 | ForEach-Object {
        Write-Host "  `"$($_.Key)`": `"$($_.Value)`""
    }
}
catch {
    Write-Error "Failed to save results to $OutputFile : $($_.Exception.Message)"
}
