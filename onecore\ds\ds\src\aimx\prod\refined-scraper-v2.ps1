param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

Write-Host "Processing: $CommandName" -ForegroundColor Green
Write-Host "URL: $URL" -ForegroundColor White

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    # --- Extract Syntax ---
    $syntax = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)<h2') {
        $syntaxSection = $matches[1]
        
        # Find all pre/code blocks in syntax section
        $syntaxMatches = [regex]::Matches($syntaxSection, '(?s)<pre><code[^>]*>(.*?)</code></pre>')
        foreach ($match in $syntaxMatches) {
            $syntaxText = $match.Groups[1].Value
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            $syntaxText = $syntaxText -replace '<[^>]+>', '' # Remove any remaining HTML tags
            $syntaxText = $syntaxText.Trim()
            
            if ($syntaxText -and $syntaxText.Length -gt 10) {
                $syntax += $syntaxText
            }
        }
    }
    
    # --- Extract Examples with Descriptions ---
    $examples = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        
        # Find each example section (h3 + div pairs)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>.*?</h3>\s*<div[^>]*>(.*?)</div>')
        
        foreach ($match in $exampleMatches) {
            $exampleContent = $match.Groups[1].Value
            
            # Extract code from pre/code block
            $code = ""
            if ($exampleContent -match '(?s)<pre><code[^>]*>(.*?)</code></pre>') {
                $code = $matches[1]
                $code = $code -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                $code = $code -replace '<[^>]+>', '' # Remove HTML tags
                $code = $code.Trim()
            }
            
            # Extract description from p tags that follow the code (exclude the code part)
            $description = ""
            # Remove the pre/code block first, then extract p tags
            $contentAfterCode = $exampleContent -replace '(?s)<pre><code[^>]*>.*?</code></pre>', ''
            $descriptionMatches = [regex]::Matches($contentAfterCode, '(?s)<p[^>]*>(.*?)</p>')
            $descriptions = @()
            foreach ($descMatch in $descriptionMatches) {
                $descText = $descMatch.Groups[1].Value
                $descText = $descText -replace '<code[^>]*>(.*?)</code>', '$1' # Keep code content but remove tags
                $descText = $descText -replace '<[^>]+>', '' # Remove other HTML tags
                $descText = $descText.Trim()

                if ($descText -and $descText.Length -gt 10) {
                    $descriptions += $descText
                }
            }

            if ($descriptions.Count -gt 0) {
                $description = $descriptions -join " "
            }

            Write-Host "    Final description before adding to examples: '$description'" -ForegroundColor Magenta
            Write-Host "    Description length: $($description.Length)" -ForegroundColor Magenta

            if ($code) {
                $exampleObj = [PSCustomObject]@{
                    Code = $code
                    Description = $description
                }

                Write-Host "    Created example object - Description: '$($exampleObj.Description)'" -ForegroundColor Magenta
                $examples += $exampleObj
            }
        }
    }
    
    # --- Output Results ---
    $result = [PSCustomObject]@{
        CommandName = $CommandName
        URL = $URL
        Syntax = $syntax
        SyntaxCount = $syntax.Count
        Examples = $examples
        ExampleCount = $examples.Count
    }
    
    Write-Host "`n=== RESULTS ===" -ForegroundColor Yellow
    Write-Host "Syntax blocks found: $($syntax.Count)" -ForegroundColor White
    foreach ($s in $syntax) {
        Write-Host "  Syntax: $($s.Split("`n")[0])..." -ForegroundColor Cyan
    }
    
    Write-Host "Examples found: $($examples.Count)" -ForegroundColor White
    foreach ($ex in $examples) {
        Write-Host "  Code: $($ex.Code.Split("`n")[0])..." -ForegroundColor Cyan
        Write-Host "  Desc: $($ex.Description.Substring(0, [Math]::Min(80, $ex.Description.Length)))..." -ForegroundColor Green
    }
    
    # Save to JSON for inspection
    $result | ConvertTo-Json -Depth 10 | Out-File "test_result_$($CommandName.Replace('-', '_')).json" -Encoding UTF8
    Write-Host "`nResult saved to: test_result_$($CommandName.Replace('-', '_')).json" -ForegroundColor Green
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
