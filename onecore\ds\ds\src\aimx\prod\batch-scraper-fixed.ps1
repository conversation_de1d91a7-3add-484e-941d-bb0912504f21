# Batch scraper for all 139 PowerShell Active Directory commands

# Import required modules
try {
    Import-Module PowerHTML -ErrorAction Stop
    Write-Host "PowerHTML module loaded successfully." -ForegroundColor Green
}
catch {
    Write-Error "The 'PowerHTML' module is not installed. Please run: Install-Module -Name PowerHTML"
    exit 1
}

# Load the original dataset to get all command names
$datasetPath = ".\netRag\data\ad_powershell_final_rag_with_frequency.json"
if (-not (Test-Path $datasetPath)) {
    Write-Error "Dataset file not found: $datasetPath"
    exit 1
}

Write-Host "Loading command dataset..." -ForegroundColor Yellow
$dataset = Get-Content $datasetPath | ConvertFrom-Json

# Extract all command names
$commands = $dataset.commands | ForEach-Object { $_.command_name } | Sort-Object
Write-Host "Found $($commands.Count) commands to process." -ForegroundColor Green

# Initialize results array
$allResults = @()
$successCount = 0
$failureCount = 0
$startTime = Get-Date

Write-Host "`nStarting batch processing..." -ForegroundColor Yellow
Write-Host "=" * 60

for ($i = 0; $i -lt $commands.Count; $i++) {
    $command = $commands[$i]
    $progress = [math]::Round(($i + 1) / $commands.Count * 100, 1)
    
    Write-Host "[$($i + 1)/$($commands.Count)] ($progress%) Processing: $command" -ForegroundColor Cyan
    
    # Construct the Microsoft Learn URL
    $url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($command.ToLower())"
    
    try {
        # Run the scraper
        $result = & ".\final-comprehensive-scraper.ps1" -URL $url
        
        if ($result) {
            # Parse the JSON result
            $commandData = $result | ConvertFrom-Json
            $allResults += $commandData
            $successCount++
            
            Write-Host "  Success - Found $($commandData.Parameters.Count) parameters, $($commandData.Examples.Count) examples" -ForegroundColor Green
        }
        else {
            Write-Host "  Failed - No data returned" -ForegroundColor Red
            $failureCount++
        }
    }
    catch {
        Write-Host "  Failed - Error: $($_.Exception.Message)" -ForegroundColor Red
        $failureCount++
    }
    
    # Add a small delay to be respectful to Microsoft's servers
    Start-Sleep -Milliseconds 500
}

# Calculate processing time
$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host "`n" + "=" * 60
Write-Host "BATCH PROCESSING COMPLETE" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "Total Commands: $($commands.Count)"
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failureCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round($successCount / $commands.Count * 100, 1))%"
Write-Host "Processing Time: $($duration.ToString('hh\:mm\:ss'))"

# Save the results
$outputPath = "comprehensive_ad_commands_dataset.json"
Write-Host "`nSaving results to: $outputPath" -ForegroundColor Yellow

$finalDataset = [PSCustomObject]@{
    metadata = [PSCustomObject]@{
        total_commands = $commands.Count
        successful_extractions = $successCount
        failed_extractions = $failureCount
        success_rate = [math]::Round($successCount / $commands.Count * 100, 1)
        processing_date = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        processing_duration = $duration.ToString('hh\:mm\:ss')
        source = "Microsoft Learn Documentation"
        scraper_version = "final-comprehensive-scraper.ps1"
    }
    commands = $allResults
}

$finalDataset | ConvertTo-Json -Depth 6 | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host "Dataset saved successfully!" -ForegroundColor Green
Write-Host "File size: $([math]::Round((Get-Item $outputPath).Length / 1MB, 2)) MB"

# Display sample of results
if ($allResults.Count -gt 0) {
    Write-Host "`nSample result (first command):" -ForegroundColor Yellow
    $allResults[0] | ConvertTo-Json -Depth 3
}

Write-Host "`nBatch processing completed successfully!" -ForegroundColor Green
