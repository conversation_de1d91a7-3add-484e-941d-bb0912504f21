Import-Module PowerHTML

Write-Host "Testing PowerHTML module..."

try {
    $html = ConvertFrom-Html -Uri 'https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust'
    Write-Host "HTML document loaded successfully"
    
    # Test basic structure
    $h1 = $html.SelectSingleNode('//h1')
    if ($h1) {
        Write-Host "H1 found: $($h1.InnerText)"
    } else {
        Write-Host "H1 not found"
    }
    
    # Check for examples section
    $examples = $html.SelectSingleNode("//div[@id='examples']")
    if ($examples) {
        Write-Host "Examples section found"
    } else {
        Write-Host "Examples section not found"
    }
    
    # Check for summary section
    $summary = $html.SelectSingleNode("//div[@id='summary']")
    if ($summary) {
        Write-Host "Summary section found"
    } else {
        Write-Host "Summary section not found"
    }
    
    # List all div IDs to understand structure
    $divs = $html.SelectNodes("//div[@id]")
    Write-Host "Found div elements with IDs:"
    foreach ($div in $divs) {
        $id = $div.Attributes["id"]
        if ($id) {
            Write-Host "  - $($id.Value)"
        }
    }

    # Check for h2 and h3 headings to understand structure
    $headings = $html.SelectNodes("//h2 | //h3")
    Write-Host "`nFound headings:"
    foreach ($heading in $headings) {
        Write-Host "  - $($heading.Name): $($heading.InnerText)"
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
