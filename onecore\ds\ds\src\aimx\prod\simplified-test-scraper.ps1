param(
    [Parameter(Mandatory=$true)]
    [string]$URL
)

Import-Module PowerHTML

try {
    $htmlDocument = ConvertFrom-Html -Uri $URL
    
    # Extract command name
    $commandName = $htmlDocument.SelectSingleNode("//h1").InnerText.Trim()
    
    # Extract Syntax (simplified)
    $syntax = @()
    $syntaxNodes = $htmlDocument.SelectNodes("//h2[text()='Syntax']/following-sibling::div[.//code]")
    foreach ($syntaxNode in $syntaxNodes) {
        $codeNode = $syntaxNode.SelectSingleNode(".//code")
        if ($null -ne $codeNode) {
            $syntaxText = $codeNode.InnerText.Trim()
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            if ($syntaxText -match "Get-\w+" -and $syntaxText -match "\[.*\]" -and -not ($syntaxText -match "PS C:\\>")) {
                $syntax += $syntaxText
            }
        }
    }
    
    # Extract Parameters (simplified - just first parameter for testing)
    $parameters = @()
    $parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3")
    $allParamTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*//table")
    
    if ($parameterHeaders.Count -gt 0 -and $allParamTables.Count -gt 0) {
        $firstParam = $parameterHeaders[0]
        $paramName = $firstParam.InnerText.Trim()
        
        if ($paramName -match "^-\w+") {
            $details = @{}
            
            # Get first table (Type table)
            if ($allParamTables.Count -gt 0) {
                $typeTable = $allParamTables[0]
                $rows = $typeTable.SelectNodes(".//tr")
                if ($null -ne $rows) {
                    foreach ($row in $rows) {
                        $cells = $row.SelectNodes(".//td")
                        if ($null -ne $cells -and $cells.Count -ge 2) {
                            $key = $cells[0].InnerText.Trim() -replace '::', '' -replace ':', ''
                            $value = $cells[1].InnerText.Trim()
                            $details[$key] = $value
                        }
                    }
                }
            }
            
            $parameters += [PSCustomObject]@{
                Name = $paramName
                Description = "Test description"
                Details = $details
            }
        }
    }
    
    # Extract Examples (simplified)
    $examples = @()
    $exampleHeaders = $htmlDocument.SelectNodes("//h2[text()='Examples']/following-sibling::h3")
    if ($exampleHeaders.Count -gt 0) {
        $firstExample = $exampleHeaders[0]
        $title = $firstExample.InnerText.Trim()
        
        $examples += [PSCustomObject]@{
            Title = $title
            Code = "Test code"
        }
    }
    
    # Output result
    $result = [PSCustomObject]@{
        CommandName = $commandName
        Syntax = $syntax
        Parameters = $parameters
        Examples = $examples
    }
    
    $result | ConvertTo-Json -Depth 10
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
