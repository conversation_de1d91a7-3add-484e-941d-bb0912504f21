<#
.SYNOPSIS
    Final comprehensive PowerShell command scraper for Microsoft Learn documentation.

.DESCRIPTION
    This script extracts complete information from Microsoft Learn PowerShell documentation pages,
    including Synopsis, Parameters with full details, and Examples with actual code and descriptions.

.PARAMETER URL
    The URL of the Microsoft Learn page to scrape.

.EXAMPLE
    .\final-comprehensive-scraper.ps1 -URL "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
#>
param (
    [Parameter(Mandatory = $true)]
    [string]$URL
)

try {
    Import-Module PowerHTML -ErrorAction Stop
}
catch {
    Write-Error "The 'PowerHTML' module is not installed. Please run: Install-Module -Name PowerHTML"
    return
}

try {
    $htmlDocument = ConvertFrom-Html -Uri $URL -ErrorAction Stop
}
catch {
    Write-Error "Failed to fetch or parse the URL: $URL. Please check the URL and your internet connection."
    return
}

# Extract command name from h1
$commandName = $htmlDocument.SelectSingleNode("//h1").InnerText.Trim()

# --- Extract Syntax ---
$syntax = @()
$syntaxHeader = $htmlDocument.SelectSingleNode("//h2[text()='Syntax']")
if ($null -ne $syntaxHeader) {
    # Get syntax blocks (simplified approach)
    $syntaxNodes = $htmlDocument.SelectNodes("//h2[text()='Syntax']/following-sibling::div[.//code]")
    foreach ($syntaxNode in $syntaxNodes) {
        $codeNode = $syntaxNode.SelectSingleNode(".//code")
        if ($null -ne $codeNode) {
            $syntaxText = $codeNode.InnerText.Trim()
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            # Only include if it looks like actual syntax (contains parameter format)
            if ($syntaxText -match "Get-\w+" -and $syntaxText -match "\[.*\]" -and -not ($syntaxText -match "PS C:\\>")) {
                $syntax += $syntaxText
            }
        }
    }
}

# --- Extract Parameters ---
$parameters = @()
$parametersHeader = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($null -ne $parametersHeader) {
    # Find all h3 parameter headers that come after Parameters h2 but before next major section
    $parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3[following-sibling::h2[1][text()='Inputs' or text()='Outputs' or text()='Notes' or text()='Related Links']]")

    # Get Type tables and Parameter Sets tables separately
    $allTypeTables = $htmlDocument.SelectNodes("//table[.//td[contains(text(), 'Type')]]")
    $allParamSetsTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")

    $paramIndex = 0
    foreach ($paramHeader in $parameterHeaders) {
        $paramName = $paramHeader.InnerText.Trim()

        # Skip CommonParameters and Input/Output types
        if ($paramName -eq "CommonParameters" -or $paramName -match "Microsoft\.ActiveDirectory" -or $paramName -eq "None or Microsoft.ActiveDirectory.Management.ADTrust") {
            continue
        }

        $description = ""
        $details = @{}

        # Get content between this h3 and the next h3 or h2 for description
        $nextNode = $paramHeader.NextSibling
        $descriptionParts = @()

        while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3')) {
            # Collect description from paragraphs
            if ($nextNode.Name -eq 'p') {
                $descriptionParts += $nextNode.InnerText.Trim()
            }

            # Collect description from lists (enumerated values)
            if ($nextNode.Name -eq 'ul') {
                $listItems = $nextNode.SelectNodes(".//li")
                if ($null -ne $listItems) {
                    foreach ($li in $listItems) {
                        $descriptionParts += $li.InnerText.Trim()
                    }
                }
            }

            $nextNode = $nextNode.NextSibling
        }

        # Combine all description parts
        $description = ($descriptionParts | Where-Object { $_.Length -gt 0 }) -join "`n"

        # Get the corresponding Type table and Parameter Sets table for this parameter
        if ($paramIndex -lt $allTypeTables.Count -and $paramIndex -lt $allParamSetsTables.Count) {
            # Type table: Parameter Properties (Type, Default value, etc.)
            $typeTable = $allTypeTables[$paramIndex]
            $rows = $typeTable.SelectNodes(".//tr")
            if ($null -ne $rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($null -ne $cells -and $cells.Count -ge 2) {
                        $key = $cells[0].InnerText.Trim()
                        $value = $cells[1].InnerText.Trim()
                        if ($key -and $value) {
                            $key = $key -replace '::', '' -replace ':', ''
                            switch ($key) {
                                "Type" { $details["Type"] = $value }
                                "Default value" { $details["Default value"] = $value }
                                "Accepted values" { $details["Accepted values"] = $value }
                                "Supports wildcards" { $details["Accept wildcard characters"] = $value }
                                "Aliases" { $details["Aliases"] = $value }
                                "DontShow" { $details["DontShow"] = $value }
                                default { $details[$key] = $value }
                            }
                        }
                    }
                }
            }

            # Parameter Sets table: (Position, Mandatory, etc.)
            $paramSetsTable = $allParamSetsTables[$paramIndex]
            $rows = $paramSetsTable.SelectNodes(".//tr")
            if ($null -ne $rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($null -ne $cells -and $cells.Count -ge 2) {
                        $key = $cells[0].InnerText.Trim()
                        $value = $cells[1].InnerText.Trim()
                        if ($key -and $value) {
                            $key = $key -replace '::', '' -replace ':', ''
                            switch ($key) {
                                "Position" { $details["Position"] = $value }
                                "Mandatory" { $details["Required"] = $value }
                                "Value from pipeline" { $details["Accept pipeline input"] = $value }
                                "Value from pipeline by property name" { $details["Accept pipeline input by property name"] = $value }
                                "Value from remaining arguments" { $details["Accept remaining arguments"] = $value }
                                default { $details[$key] = $value }
                            }
                        }
                    }
                }
            }

            # Move to next parameter
            $paramIndex++
        }

        $parameters += [PSCustomObject]@{
            Name        = $paramName
            Description = $description
            Details     = $details
        }
    }
}

# --- Extract Examples ---
$examples = @()
$examplesHeader = $htmlDocument.SelectSingleNode("//h2[text()='Examples']")
if ($null -ne $examplesHeader) {
    # Find all h3 example headers that come after Examples h2 but before Parameters h2
    $exampleHeaders = $htmlDocument.SelectNodes("//h2[text()='Examples']/following-sibling::h3[following-sibling::h2[1][text()='Parameters']]")
    
    foreach ($exampleHeader in $exampleHeaders) {
        $title = $exampleHeader.InnerText.Trim()
        
        # Skip if this looks like a parameter (starts with -)
        if ($title -match "^-\w+") { continue }
        
        $description = ""
        $code = ""
        
        # Get content between this h3 and the next h3 or h2
        $nextNode = $exampleHeader.NextSibling
        
        while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3')) {
            # Look for description in paragraphs that come before code
            if ($nextNode.Name -eq 'p' -and [string]::IsNullOrEmpty($description)) {
                $description = $nextNode.InnerText.Trim()
            }
            
            # Look for code in div elements
            if ($nextNode.Name -eq 'div') {
                $codeNodes = $nextNode.SelectNodes(".//code")
                if ($null -ne $codeNodes) {
                    foreach ($codeNode in $codeNodes) {
                        $codeText = $codeNode.InnerText.Trim()
                        # Clean up HTML entities
                        $codeText = $codeText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                        # Only capture PowerShell commands
                        if ($codeText -match "PS C:\\>" -or $codeText -match "Get-|Set-|New-|Remove-|Add-") {
                            $code = $codeText
                            break
                        }
                    }
                }
            }
            
            $nextNode = $nextNode.NextSibling
        }
        
        $examples += [PSCustomObject]@{
            Title       = $title
            Code        = $code
        }
    }
}

# --- Combine all information ---
$commandInfo = [PSCustomObject]@{
    CommandName = $commandName
    Syntax     = $syntax
    Parameters = $parameters
    Examples   = $examples
}

# --- Output as JSON ---
$commandInfo | ConvertTo-Json -Depth 5 | Write-Output
