﻿{
    "aCSMaxDurationPerFlow":  "The maximum duration, in seconds, of any single flow.",
    "ipsecPolicyReference":  "The distinguished name of the related Internet Protocol security (IPsec) policy.",
    "msDS-TasksForAzTaskBL":  "Backward link from Az-Task to the Az-Task objects that link to it.",
    "attributeID":  "The unique X.500 OID for identifying an attribute.",
    "msSFU30Name":  "Contains the name of a map.",
    "msKds-RootKeyData":  "Root key.",
    "msWMI-Parm3":  "The ms-WMI-Parm3 attribute is reserved for internal use.",
    "msSFU30CryptMethod":  "Contains the method that is used to encrypt UNIX passwords.",
    "company":  "The user\u0027s company name.",
    "maxRenewAge":  "This attribute determines the time period, in days, during which a user\u0027s ticket-granting ticket (TGT) can be renewed for purposes of Kerberos authentication. The default setting is 7 days in the Default Domain Group Policy object (GPO).",
    "macAddress":  "Contains the MAC address in maximal, colon-separated hexadecimal notation.",
    "msKds-SecretAgreementAlgorithmID":  "The name of the secret agreement algorithm to be used with public keys.",
    "packageName":  "This attribute contains the deployment name for an application.",
    "msTSExpireDate2":  "Expiration date of the second TS per user CAL.",
    "cAWEBURL":  "URL for http connection to a certification authority.",
    "parentCACertificateChain":  "DER-encoded X.509v3 certificate for the parent certification authority.",
    "originalDisplayTable":  "The MAPI (original) display table for an address entry.",
    "ipsecFilterReference":  "The Ipsec-Filter-Reference attribute is for internal use only.",
    "presentationAddress":  "Specifies a presentation address associated with an object that represents an OSI application entity.",
    "fileExtPriority":  "This attribute contains a list of file name extensions in an application package and their associated priorities.",
    "serverReferenceBL":  "Found in the domain naming context. The distinguished name of a computer under the sites folder.",
    "dhcpServers":  "Contains a list of servers that are authorized in the enterprise.",
    "msTSBrokenConnectionAction":  "Terminal Services session Broken Connection Action specifies the action to take when a Terminal Services session limit is reached.",
    "msKds-KDFParam":  "Parameters for the key derivation algorithm.",
    "versionNumberHi":  "A general purpose major version number.",
    "objectVersion":  "This can be used to store a version number for the object.",
    "msDFSR-ConflictPath":  "Contains the full path of the conflict directory.",
    "possSuperiors":  "The list of objects that can contain this class.",
    "siteObjectBL":  "The list of distinguished names for subnets that belong to this site.",
    "msSPP-InstallationId":  "Installation ID (IID) used for phone activation of the Active Directory forest",
    "msSFU30IntraFieldSeparator":  "Contains the intra-field separators for each NIS map.",
    "msDS-DnsRootAlias":  "Used to store the domain alias.",
    "scriptPath":  "This attribute specifies the path for the user\u0027s logon script. The string can be null.",
    "msDFSR-ReplicationGroupGuid":  "Contains the replication group GUID.",
    "serverRole":  "For compatibility with pre-Windows 2000 Server servers. A computer running Windows NT Server can be a standalone server, a primary domain controller (PDC), or a backup domain controller (BDC).",
    "msDS-FilterContainers":  "A multiple-valued string that contains the names of classes used to determine which container types should be shown by the Active Directory Users and Computers snap-in when filtering.",
    "msAuthz-MemberRulesInCentralAccessPolicyBL":  "Backlink for ms-Authz-Member-Rules-In-Central-Access-Policy. For a central access rule object, this attribute references one or more central access policies that point to it.",
    "frsComputerReferenceBL":  "Reference to replica sets to which this computer belongs.",
    "msDS-EnabledFeature":  "Enabled optional features.",
    "msDS-HasInstantiatedNCs":  "DS replication state information, analogous to (and a superset of) the existing attributes hasMasterNCs and hasPartialReplicaNCs. To be used by the KCC when setting up replication partners.",
    "lastSetTime":  "The last time the secret was changed. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC).",
    "canUpgradeScript":  "This attribute stores the list of application packages that can be upgraded by or which can upgrade this application package.",
    "syncWithSID":  "For the SAM builtin group object/ local policy synchronization, this is the local group to which an object corresponds.",
    "telexNumber":  "A list of alternate telex numbers.",
    "deltaRevocationList":  "List of certificates that have been revoked since the last delta update.",
    "operatingSystemServicePack":  "The operating system service pack ID string (for example, SP3).",
    "pwdProperties":  "Password Properties. Part of Domain Policy. A bitfield to indicate complexity and storage restrictions.",
    "printOwner":  "A user-supplied owner string.",
    "msDS-OptionalFeatureFlags":  "An integer value that contains flags that define behavior of an optional feature in Active Directory.",
    "msTSLSProperty02":  "Placeholder terminal server property 02.",
    "aCSEventLogLevel":  "RSVP logging level.",
    "msDS-AzApplicationName":  "A string that uniquely identifies an application object.",
    "notes":  "Free text for notes on object.",
    "mSMQInterval1":  "In MSMQ mixed-mode, default replication time within a site.",
    "msWMI-IntDefault":  "The default value for WMI 32-bit integer parameter objects.",
    "minPwdAge":  "The minimum amount of time, in 100-nanosecond intervals, that a password is valid.",
    "msDNS-NSEC3UserSalt":  "An attribute that defines a user-specified NSEC3 salt string to use when signing the DNS zone. If empty, random salt will be used.",
    "priorSetTime":  "The previous time set for a secret.",
    "showInAddressBook":  "This attribute is used to indicate in which MAPI address books an object will appear. It is usually maintained by the Exchange Recipient Update Service.",
    "msRASSavedFramedRoute":  "The msRASSavedFramedRoute attribute is used internally. Do not modify this value directly.",
    "otherMobile":  "A list of alternate mobile phone numbers.",
    "msSPP-CSVLKPartialProductKey":  "Last 5 characters of CSVLK product-key used to create the Activation Object",
    "mS-SQL-Location":  "A user defined string. Default is set to Location.",
    "uniqueIdentifier":  "The uniqueIdentifier attribute type specifies a \"unique identifier\" for an object represented in the Directory.",
    "preferredOU":  "The Organizational Unit to show by default on user\u0027 s desktop.",
    "replicaSource":  "This attribute contains the GUID of a replication source.",
    "msDS-UserPasswordExpired":  "Indicates whether the password for the account that this attribute references has expired. True if the password has expired; otherwise, False.",
    "msDS-TopQuotaUsage":  "The list of top quota users currently in the directory database, ordered by decreasing quota usage.",
    "aCSNonReservedMaxSDUSize":  "The ACS-Non-Reserved-Max-SDU-Size attribute is for internal use only. Based on RFC2814.",
    "msSFU30MaxGidNumber":  "Contains the maximum number of groups migrated to an NIS domain.",
    "rIDManagerReference":  "The Distinguished Name for the RID Manager of an object.",
    "photo":  "An object encoded in G3 fax as explained in recommendation T.4, with an ASN.1 wrapper to make it compatible with an X.400 BodyPart as defined in X.420.",
    "mSMQForeign":  "Indicates whether this computer is a foreign MSMQ computer.",
    "otherTelephone":  "A list of alternate office phone numbers.",
    "msDFSR-CommonStagingPath":  "Full path of the common staging directory.",
    "msDFSR-RdcMinFileSizeInKb":  "Contains the minimum file size, in kilobytes, to which to apply RDC.",
    "mSMQVersion":  "The version number of MSMQ DS information.",
    "aCSNonReservedMinPolicedSize":  "The ACS-Non-Reserved-Min-Policed-Size attribute is for internal use only. Based on RFC2814.",
    "mS-SQL-LastUpdatedDate":  "This value represents the time that SQL last updated Active Directory.",
    "ms-net-ieee-80211-GP-PolicyData":  "Contains all of the settings and data that make up a Group Policy configuration for 802.11 wireless networks.",
    "adminPropertyPages":  "The order number and GUID of the property pages for an object to be displayed on Active Directory administration screens. For more information see the document, Extending the User Interface for Directory Objects.",
    "gidNumber":  "Contains an integer value that uniquely identifies a group in an administrative domain.",
    "title":  "Contains the user\u0027s job title. This property is commonly used to indicate the formal job title, such as Senior Programmer, rather than occupational class, such as programmer. It is not typically used for suffix titles such as Esq. or DDS.",
    "fRSVersion":  "The version number and build date.",
    "fRSExtensions":  "Binary data used by file replication.",
    "msDS-ClaimSource":  "For a claim type, this attribute indicates the source of the claim type. For example, the source can be certificate.",
    "rpcNsProfileEntry":  "The list of entries for the current priority.",
    "printMaxResolutionSupported":  "The maximum printer resolution.",
    "msSFU30NSMAPFieldPosition":  "Contains the field position that is used to extract the key from a nonstandard map.",
    "msCOM-DefaultPartitionLink":  "A link used to identify the \u0027default\u0027 COM+ partition in a COM+ PartitionSet.",
    "friendlyNames":  "List of default display name definitions supported by a catalog.",
    "trustDirection":  "The direction of a trust.",
    "msAuthz-MemberRulesInCentralAccessPolicy":  "For a central access policy, this attribute identifies the central access rules that make up the policy.",
    "mS-SQL-ServiceAccount":  "A user defined string. The default is set to ServiceAccount.",
    "msWMI-PropertyName":  "The target policy object name for a parameter object.",
    "msPKI-Supersede-Templates":  "Specifies the names of the certificate templates that are superseded by the current template.",
    "msSFU30KeyAttributes":  "Contains the names of the attributes that the NIS server uses as keys to a search map.",
    "msPKI-CredentialRoamingTokens":  "Storage of encrypted user credential token BLOBs for roaming.",
    "altSecurityIdentities":  "Contains mappings for X.509 certificates or external Kerberos user accounts to this user for the purpose of authentication.",
    "nisMapName":  "Contains the name of the map to which the object belongs.",
    "aCSMaxTokenRatePerFlow":  "The maximum token rate any single flow may have for a given user.",
    "fromServer":  "The distinguished name of the replication source server.",
    "mS-SQL-CharacterSet":  "The character set for the current instance of SQL Server.",
    "marshalledInterface":  "Marshaled interface Pointer.",
    "msDS-ClaimTypeAppliesToClass":  "For a claim type object, this linked attribute points to the AD security principal classes that for which claims should be issued. (For example, a link to the user class).",
    "documentIdentifier":  "The documentIdentifier attribute type specifies a unique identifier for a document.",
    "dhcpUniqueKey":  "The dhcp-Unique-Key attribute is not currently used.",
    "msTPM-TpmInformationForComputerBL":  "This attribute links a TPM object to the Computer objects associated with it.",
    "fRSServiceCommand":  "A Unicode string that an admin can set to pass a command to every replica set member. Not used.",
    "winsockAddresses":  "A Winsock service address.",
    "roleOccupant":  "The distinguished name of an object that fulfills an organizational role.",
    "buildingName":  "The name of the building where an organization or organizational unit is based.",
    "dnsAllowXFR":  "The Dns-Allow-XFR attribute is not currently used.",
    "lDAPDisplayName":  "The name used by LDAP clients, such as the ADSI LDAP provider, to read and write the attribute by using the LDAP protocol.",
    "adminContextMenu":  "The order number and GUID of the context menu to be used on administration screens.",
    "msPKI-Minimal-Key-Size":  "Indicates the minimum private key size.",
    "addressType":  "A character string that describes the format of the user\u0027s address. Address types map to address formats. That is, by looking at a recipient\u0027s address type, client applications can determine how to format an address that is appropriate for the recipient.",
    "msSFU30PosixMember":  "Contains the DN display name of the user\u0027s part of a group.",
    "hideFromAB":  "Do not use this attribute.",
    "mSMQDigestsMig":  "In MSMQ mixed-mode, contains the previous value of mSMQDigests.",
    "msWMI-QueryLanguage":  "Identifies a WQL query language.",
    "gPCFunctionalityVersion":  "The version of the Group Policy Editor that created this object.",
    "msDS-SCPContainer":  "ms-DS-SCP-container attribute.",
    "addressSyntax":  "A grammar for encoding the display table properties as a string.",
    "domainPolicyObject":  "Reference to the policy object that defines the Local Security Authority policy for the host domain.",
    "siteList":  "List of sites connected to this link object.",
    "pKIExpirationPeriod":  "The validity period for the certificate template.",
    "tombstoneLifetime":  "The number of days before a deleted object is removed from the directory services. This assists in removing objects from replicated servers and preventing restores from reintroducing a deleted object. This value is in the Directory Service object in the configuration NIC.",
    "msDS-isGC":  "Identifies the state of the Global Catalog on the DC.",
    "fRSControlOutboundBacklog":  "Warning/Error level pair for outbound backlog (number of files).",
    "msAuthz-ProposedSecurityPolicy":  "For a Central Access Policy Entry, defines the proposed security policy of the objects the CAPE is applied to.",
    "msDFS-LinkIdentityGUIDv2":  "To be set only when the link is created. Stable across rename or move as long as the link is not replaced by another link that has the same name.",
    "msDS-AllowedToDelegateTo":  "This is an attribute on service account (computer or user account) objects. It contains a list of Service Principal Names (SPNs). This attribute is used to configure a service so that it can obtain service tickets that can be used for Constrained Delegation.",
    "authorityRevocationList":  "Cross certificate, Certificate Revocation List.",
    "mSMQPrivacyLevel":  "The privacy level that is required by the queue.",
    "generationQualifier":  "Indicates a person generation. For example, Jr. or II.",
    "msieee80211-ID":  "An identifier used for a wireless policy object on AD.",
    "msDS-RevealedListBL":  "Backward link attribute for ms-DS-Revealed-List.",
    "msDS-AzLDAPQuery":  "A string that defines the LDAP query (max length 4096) which determines the membership of a user object to the group.",
    "isEphemeral":  "The Is-Ephemeral attribute is not currently used.",
    "msTSHomeDirectory":  "Terminal Services Home Directory specifies the Home directory for the user.",
    "pKIOverlapPeriod":  "The period by when the certificate should be renewed before it is expired.",
    "versionNumber":  "A general purpose version number.",
    "mSMQServiceType":  "The type of service provided by MSMQ installed on this computer.",
    "msDS-Integer":  "An attribute for storing an integer.",
    "classDisplayName":  "The object name to be displayed in dialog boxes.",
    "houseIdentifier":  "Specifies a linguistic construct used to identify a particular building, for example, a house number or house name relative to a street, avenue, town, city, and so on.",
    "ipsecData":  "The Ipsec-Data attribute is for internal use only.",
    "remoteSourceType":  "Type of pointer to foreign object.",
    "fRSDSPoll":  "DS Polling interval for the File Replication engine.",
    "msDS-ObjectReferenceBL":  "Backward link for ms-DS-Object-Reference.",
    "msAuthz-LastEffectiveSecurityPolicy":  "For a central access rule, this attribute defines the permission that was last applied to the objects the Central Access Rule is applied to.",
    "o":  "The name of the company or organization.",
    "localizationDisplayId":  "Used to index into the file Extrts.mc to get the localized displayName for the objects for UI purposes.",
    "mSMQTransactional":  "Indicates whether the queue is transactional.",
    "syncAttributes":  "Attributes that contain information on the sync objects.",
    "preferredLanguage":  "The preferred written or spoken language for a person.",
    "auditingPolicy":  "Auditing policy for the local policy.",
    "msDFSR-MemberReferenceBL":  "Contains the backward link for the ms-DFSR-MemberReference attribute.",
    "modifyTimeStamp":  "A computed attribute that represents the date when this object was last changed. This value is not replicated.",
    "printEndTime":  "The time a print queue stops servicing jobs.",
    "remoteSource":  "Backward pointer to foreign objects.",
    "msDS-IsDomainFor":  "Backward link for ms-DS-Has-Domain-NCs. Identifies which DCs hold that partition as their primary domain.",
    "replUpToDateVector":  "Tracks internal replication state information for an entire NC. Information here can be extracted in public form through the API DsReplicaGetInfo(). Present on all NC root objects.",
    "msDS-Security-Group-Extra-Classes":  "The common names of the nonstandard classes that can be added to a security group through the Active Directory Users and Computers snap-in.",
    "msDS-AzApplicationVersion":  "A version number to indicate that the AzApplication is updated.",
    "msDS-SourceObjectDN":  "Contains the string representation of the distinguished name of the object in another forest that corresponds to this object.",
    "operatorCount":  "Operator count.",
    "msDS-LocalEffectiveRecycleTime":  "Recycle time of the object in the local DIT.",
    "msKds-PublicKeyLength":  "The length of the secret agreement public key.",
    "auxiliaryClass":  "The list of auxiliary classes to be associated with this class.",
    "accountExpires":  "The date when the account expires. This value represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of 0 or 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicates that the account never expires. Accounts configured to never expire may have either value, depending on whether they were originally configured with an expiration value, with 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicating that the account has not been previously configured to expire.",
    "dhcpType":  "The type of DHCP server. This attribute is set on all objects of objectClass dHCPClass. Its value defines the type of object:",
    "trustPosixOffset":  "The Portable Operating System Interface (POSIX) offset for the trusted domain.",
    "msDS-MembersForAzRoleBL":  "Backward link from member application group or user to Az-Role objects linking to it.",
    "msSFU30SearchContainer":  "Contains the identifier of an object that specifies where each search will begin.",
    "msDS-TasksForAzRoleBL":  "Backward link from Az-Task to Az-Role objects that link to it.",
    "msDS-NC-Replica-Locations":  "A list of servers that are the replica set for the corresponding Non-Domain Naming Context.",
    "maxTicketAge":  "This attribute determines the maximum amount of time, in hours, that a user\u0027s ticket-granting ticket (TGT) can be used for the purpose of Kerberos authentication. When a user\u0027s TGT expires, a new one must be requested, or the existing one must be renewed. By default, this setting is set to 10 hours in the Default Domain Group Policy object (GPO).",
    "msDFSR-CommonStagingSizeInMb":  "Size, in megabytes, of the common staging directory.",
    "printMinYExtent":  "The minimum vertical print region.",
    "employeeType":  "The job category for an employee.",
    "perMsgDialogDisplayTable":  "The Per Message options MAPI display table.",
    "msDFSR-Keywords":  "Contains the user-defined keywords.",
    "maxPwdAge":  "The maximum amount of time, in 100-nanosecond intervals, a password is valid. This value is stored as a large integer that represents the number of 100-nanosecond intervals from the time the password was set before the password expires.",
    "st":  "The name of a user\u0027s state or province.",
    "serverReference":  "Found in a site computer object. Contains the distinguished name of the domain controller in the domain naming context.",
    "timeRefresh":  "This attribute has the interval during which a resource record that is contained in an Active Directory integrated zone should be refreshed for the DNS server. The default interval is 7 days.",
    "msPKI-RA-Signature":  "Specifies the number of enrollment registration authority signatures that are required in an enrollment request.",
    "msDS-PromotionSettings":  "For a computer, contains an XML string to be used for delegated DSA promotion.",
    "showInAdvancedViewOnly":  "TRUE if this attribute is to be visible in the Advanced mode of the UI.",
    "mSMQSiteGates":  "The list of DNs for MSMQ routing servers, through which all traffic between sites must be routed.",
    "msDS-MembersForAzRole":  "List of member application groups or users linked to Az-Role.",
    "vendor":  "This attribute identifies the vendor for an application.",
    "timeVolChange":  "This attribute indicates the last time that a file in the remote storage volume was changed.",
    "msDFSR-FileFilter":  "Contains the filter string that is used to filter files.",
    "fRSLevelLimit":  "Limit depth of directory tree to replicate for file replication.",
    "searchFlags":  "Contains a set of flags that specify search and indexing information for an attribute. See Remarks.",
    "userClass":  "The userClass attribute type specifies a category of computer user.",
    "dnsSecureSecondaries":  "The Dns-Secure-Secondaries attribute is not currently used.",
    "mSMQDigests":  "An array of digests of the corresponding certificates in attribute mSMQ-Sign-Certificates. They are used for mapping a digest into a certificate.",
    "printOrientationsSupported":  "The page rotation for landscape printing.",
    "msDS-DisableForInstancesBL":  "Backward link reference to the ms-DS-Service-Connection-Point-Publication-Service object.",
    "msDS-NonMembersBL":  "Backward link from non-member group or user to Az groups that link to it (same functionality as Non-Security-Member-BL).",
    "addressEntryDisplayTableMSDOS":  "The MAPI display table for an address entry for MSDOS client.",
    "msSPP-CSVLKSkuId":  "SKU ID of CSVLK product-key used to create the Activation Object",
    "mSMQNt4Flags":  "The MSMQ-Nt4-Flags attribute contains MSMQ mixed-mode information.",
    "userParameters":  "Parameters of the user. Points to a Unicode string that is set aside for use by applications. This string can be a null string, or it can have any number of characters before the terminating null character. Microsoft products use this member to store user data specific to the individual program.",
    "aCSNonReservedTokenSize":  "The ACS-Non-Reserved-Token-Size attribute is for internal use only. Based on RFC2814.",
    "msRADIUSFramedIPAddress":  "The msRADIUSFramedIPAddress attribute is used internally. Do not modify this value directly.",
    "msDNS-NSEC3OptOut":  "An attribute used to define whether or not the DNS zone should be signed using NSEC opt-out.",
    "MSMQ-MulticastAddress":  "This is part of an MSMQ object, it is set by calling API to MQCreateQueue or MQSetProperties. It controls whether MSMQ will accept messages from a multicast address.",
    "printMediaReady":  "A list of available media for a printer.",
    "extendedClassInfo":  "A multi-valued property that contains strings that represent additional information for each class. Each value contains the governsID, lDAPDisplayName, and schemaIDGUID of the class.",
    "msDS-QuotaAmount":  "The assigned quota in terms of number of objects owned in the database.",
    "objectSid":  "A binary value that specifies the security identifier (SID) of the user. The SID is a unique value used to identify the user as a security principal.",
    "defaultLocalPolicyObject":  "A reference to a Policy object that defines the local policy for the host object.",
    "msDFSR-ComputerReference":  "Contains a forward link to a Computer object.",
    "primaryGroupID":  "Contains the relative identifier (RID) for the primary group of the user. By default, this is the RID for the Domain Users group.",
    "cost":  "Contains the relative cost for routing messages through a particular site connector.",
    "msRADIUS-SavedFramedInterfaceId":  "Indicates the IPv6 interface identifier to be configured for the user.",
    "mS-SQL-Size":  "The size, in kilobytes, of the database.",
    "versionNumberLo":  "A general purpose minor version number.",
    "msTSMaxDisconnectionTime":  "Terminal Services session Maximum Disconnection Time is the maximum amount of time, in minutes, that a disconnected Terminal Services session remains active on the terminal server.",
    "sAMAccountType":  "This attribute contains information about every account type object. You can enumerate a list of account types or you can use the Display Information API to create a list. Because computers, normal user accounts, and trust accounts can also be enumerated as user objects, the values for these accounts must be a contiguous range.",
    "info":  "The user\u0027s comments. This string can be a null string.",
    "terminalServer":  "Opaque data used by the Windows NT terminal server.",
    "domainPolicyReference":  "The Distinguished Name of a domain policy object that a policy object copies from.",
    "msWMI-Parm4":  "The ms-WMI-Parm4 attribute is reserved for internal use.",
    "msTSProperty02":  "Reserved for future use.",
    "msTPM-OwnerInformationTemp":  "This attribute contains temporary owner information for a particular TPM.",
    "aCSPermissionBits":  "Allows multicast flow origination for a given user.",
    "msDFSR-MemberReference":  "Contains the forward link to the ms-DFSR-Member object.",
    "msDNS-SecureDelegationPollingPeriod":  "An attribute that defines in seconds the time between polling attempts for child zone key rollovers.",
    "mSMQPrevSiteGates":  "The MSMQ-Prev-Site-Gates attribute contains MSMQ mixed-mode information.",
    "loginShell":  "Contains the path to the login shell.",
    "msDS-NCReplOutboundNeighbors":  "Replication partners for this partition. This server sends replication data to these other servers, which act as destinations. This server will notify these other servers when new data is available.",
    "msTSMaxIdleTime":  "Terminal Services session Maximum Idle Time is the maximum amount of time, in minutes, that the Terminal Services session can remain idle.",
    "msWMI-Int8Max":  "The maximum value for a WMI 64-bit integer parameter object.",
    "mSMQOSType":  "The operating system type.",
    "aCSMinimumPolicedSize":  "The ACS-Minimum-Policed-Size attribute is for internal use only. Based on RFC2210.",
    "msDS-TransformationRulesCompiled":  "Blob containing compiled transformation rules.",
    "msDS-PSOAppliesTo":  "Links to objects that this password settings object applies to.",
    "userWorkstations":  "Contains the NetBIOS or DNS names of the computers running Windows NT Workstation or Windows 2000 Professional from which the user can log on. Each NetBIOS name is separated by a comma. Multiple names should be separated by commas.",
    "schemaVersion":  "The version number for the schema.",
    "msExchHouseIdentifier":  "This attribute contains an address for a contact in an Exchange server address book.",
    "mSMQDsServices":  "Indicates whether the MSMQ installed on this computer provides MSMQ DS services.",
    "printColor":  "TRUE if a printer can print in color.",
    "msPKI-Private-Key-Flag":  "Contains the private key related flags.",
    "netbootMirrorDataFile":  "The Netboot-Mirror-Data-File attribute is reserved for internal use.",
    "msDS-QuotaEffective":  "The effective quota for a security principal computed from the assigned quotas for a directory partition.",
    "dNReferenceUpdate":  "If an object is renamed, this attribute is used to track all of the previous and current names that have been assigned to an object so that linked objects can still find it.",
    "allowedChildClasses":  "Classes that can be contained by a class.",
    "printRateUnit":  "Driver-supplied print rate unit.",
    "serialNumber":  "Part of X.500 specification. Not used by Active Directory.",
    "initialAuthOutgoing":  "Contains information about an initial outgoing authentication sent by the authentication server for this domain to the client that requested authentication. The server that uses this attribute receives the authorization from the authentication server and sends it to the client.",
    "msSFU30NetgroupUserAtDomain":  "Contains part of the netgroup map that represents computed strings, such as \"user@domain\".",
    "msIIS-FTPDir":  "This attribute determines the user home directory relative to the file server share. It is used in conjunction with ms-IIS-FTP-Root to determine the FTP user home directory.",
    "pKTGuid":  "Unique ID of a given Distributed File System Partition Knowledge Table.",
    "wbemPath":  "References to objects in other ADSI namespaces.",
    "isMemberOfPartialAttributeSet":  "If TRUE, this attribute is replicated to the global catalog.",
    "signatureAlgorithms":  "This attribute indicates the type of algorithm that must be used to decode a digital signature during the authentication process.",
    "networkAddress":  "The TCP/IP address for a network segment. Also called the subnet address.",
    "documentLocation":  "The documentLocation attribute type specifies the location of the document original.",
    "meetingOriginator":  "The person who scheduled the meeting.",
    "msDS-DateTime":  "An attribute for storing a date and time value.",
    "msDFSR-DfsPath":  "Contains the full path of the associated Distributed File System (DFS) link for DFS Replication service support.",
    "schemaInfo":  "An internal binary value used to detect schema changes between DCs and force a schema NC replication cycle before replicating any other NC. Used to resolve ties when the schema FSMO is seized and a change is made on more than one DC.",
    "proxiedObjectName":  "This attribute is used internally by Active Directory to help track interdomain moves.",
    "managedObjects":  "Contains the list of objects that are managed by the user. The objects listed are those that have the property managedBy property set to this user. Each item in the list is a linked reference to the managed object.",
    "msRADIUSCallbackNumber":  "The msRADIUSCallbackNumber attribute is used internally. Do not modify this value directly.",
    "msPKI-Enrollment-Flag":  "Contains the enrollment related flags.",
    "msDS-PrincipalName":  "Account name for the security principal (constructed).",
    "msDS-DefaultQuota":  "The default quota that will apply to a security principal that creates an object in the NC if no quota specification exists that covers the security principal.",
    "fRSRootPath":  "Path to root of replicated file system tree.",
    "mSMQBasePriority":  "The base priority of messages transmitted to this queue.",
    "fRSTimeLastCommand":  "The time in which the last command was executed.",
    "cAUsages":  "List of OID/CSP name concatenations.",
    "upgradeProductCode":  "This attribute contains the product code of other packages, such as applications, that can be upgraded by this package, or that can upgrade this package.",
    "mS-DS-ConsistencyChildCount":  "This attribute is used to check consistency between the directory and another object, database, or application, by comparing a count of child objects.",
    "msDS-ClaimIsSingleValued":  "For a claim type object, this attribute identifies if the claim type or resource property can only contain single value.",
    "msDS-FailedInteractiveLogonCount":  "The total number of failed interactive logons since this feature was turned on.",
    "printRate":  "Driver-supplied print rate.",
    "moniker":  "The name or path for a COM object.",
    "msDFSR-ReadOnly":  "Specifies whether the content is read-only or read/write.",
    "msDS-LockoutThreshold":  "Lockout threshold for the lock out of user accounts.",
    "dhcpUpdateTime":  "The dhcp-Update-Time attribute is not currently used.",
    "msDS-PasswordHistoryLength":  "Password history length for user accounts.",
    "cOMOtherProgId":  "List of other program ID strings for the host class.",
    "msWMI-Int8Default":  "Default value for WMI 64-bit integer parameter objects.",
    "mSMQEncryptKey":  "The computer\u0027s public encryption key.",
    "msDS-ManagedPasswordPreviousId":  "This constructed attribute contains the key identifier for the previous managed password data for a group MSA.",
    "mustContain":  "The list of mandatory attributes for a class. These attributes must be specified when an instance of the class is created.",
    "rIDSetReferences":  "List of references to RID-Set objects that manage Relative Identifier (RID) allocation.",
    "operatingSystemVersion":  "The operating system version string, for example, 4.0.",
    "netbootCurrentClientCount":  "The netboot-Current-Client-Count attribute is reserved for internal use.",
    "msWMI-ID":  "A unique ID for an object instance. Indexed in the Global Catalog.",
    "dSUIAdminNotification":  "This is a list of the GUIDs of COM objects that support a callback interface that DSAdmin calls when an action has occurred on an object through the UI.",
    "rootTrust":  "The Distinguished Name of another Cross-Ref.",
    "mS-SQL-Status":  "The status for the SQL server. The status is TRUE if the server is running.",
    "builtinCreationTime":  "The Builtin-Creation-Time attribute is used to support replication to Windows NT 4.0 domains.",
    "middleName":  "Additional names for a user. For example, middle name, patronymic, matronymic, or others.",
    "maxStorage":  "The maximum amount of disk space the user can use. Use the value specified in USER_MAXSTORAGE_UNLIMITED to use all available disk space.",
    "msDFSR-ReplicationGroupType":  "Contains the replication group type.",
    "msDS-KeyVersionNumber":  "The Kerberos version number of the current key for this account. This is a constructed attribute.",
    "mayContain":  "The list of optional attributes for a class.",
    "addressEntryDisplayTable":  "The display table for an address entry.",
    "globalAddressList":  "This attribute is used on a Microsoft Exchange container to store the distinguished name of a newly created global address list (GAL). This attribute must have an entry before you can enable Messaging Application Programming Interface (MAPI) clients to use a GAL.",
    "superiorDNSRoot":  "This is a system attribute that is used for referrals generation.",
    "mS-SQL-Name":  "The name of a SQL Server instance. The default is MSSQLSERVER.",
    "otherPager":  "A list of alternate pager numbers.",
    "msDS-TasksForAzTask":  "List of tasks linked to Az-Task.",
    "meetingContactInfo":  "Contact information for a meeting.",
    "msDS-USNLastSyncSuccess":  "The USN at which the last successful replication synchronization occurred.",
    "domainWidePolicy":  "This is for user extensible policy to be replicated to the clients.",
    "c":  "The country/region in the address of the user. The country/region is represented as a 2-character code based on ISO-3166.",
    "netbootTools":  "The netboot-Tools attribute is reserved for internal use.",
    "monikerDisplayName":  "The display name for a moniker.",
    "msTSEndpointPlugin":  "This attribute represents the name of the plug-in that handles the orchestration.",
    "msDFSR-ConflictSizeInMb":  "Contains the size, in megabytes, of the conflict directory.",
    "structuralObjectClass":  "This constructed attribute stores a list of classes contained in a class hierarchy, including abstract classes. This list does contain dynamically linked auxiliary classes.",
    "Enabled":  "This attribute is used to signify whether a given crossRef is enabled.",
    "division":  "The user\u0027s division.",
    "fRSFaultCondition":  "The Fault Condition for a Member.",
    "domainReplica":  "Unicode String Attribute, gives the list of Windows NT 4.0 Replication Domain Controllers.",
    "iPSECNegotiationPolicyAction":  "The IPSEC-Negotiation-Policy-Action attribute is for internal use only.",
    "msNPCalledStationID":  "The msNPCalledStationID attribute is used internally. Do not modify this value directly.",
    "documentTitle":  "The documentTitle attribute type specifies the title of a document.",
    "netbootNewMachineNamingPolicy":  "Indicates the naming scheme which new client computer accounts will use.",
    "textEncodedORAddress":  "This attribute is used to support X.400 addresses in a text format.",
    "aCSEnableACSService":  "True if ACS service is to be enabled.",
    "nCName":  "The distinguished name of the Naming Context for the object.",
    "cACertificateDN":  "Full distinguished name from the CA certificate.",
    "url":  "A list of alternate webpages.",
    "mS-SQL-MultiProtocol":  "The RPC connection point.",
    "unixUserPassword":  "Contains a user password that is compatible with a UNIX system.",
    "fRSTimeLastConfigChange":  "The time in which the last configuration change was accepted.",
    "msDS-ByteArray":  "An attribute for storing binary data.",
    "userCert":  "Nortel v1 or DMS certificates.",
    "msTSPrimaryDesktop":  "This attribute represents the forward link to a user\u0027s primary desktop.",
    "msDS-AzMinorVersion":  "Minor version number for AzRoles.",
    "msDS-ClaimSharesPossibleValuesWithBL":  "For a claim type object, this attribute indicates that the possible values described in ms-DS-Claim-Possible-Values are being referenced by other claim type objects.",
    "carLicense":  "Vehicle license or registration plate.",
    "machineRole":  "Role for a machine: DC, Server, or Workstation.",
    "cOMTreatAsClassId":  "Treat-As string GUID CLSID for the host class.",
    "mS-DS-ReplicatesNCReason":  "Attribute of ntdsConnection object that indicates why (or whether) the KCC shows the connection is useful in the replication topology. Is multiple-valued and has DistName+Binary syntax, where the binary part is an int-size bitfield.",
    "msPKI-Certificate-Name-Flag":  "Contains the flags related to constructing the subject name in an issued certificate.",
    "governsID":  "The unique object ID of the class defined by this Class-Schema object.",
    "printShareName":  "The printer\u0027s share name.",
    "managedBy":  "The distinguished name of the user that is assigned to manage this object.",
    "mS-SQL-Build":  "The current version, including build number.",
    "msRADIUS-FramedIpv6Prefix":  "Indicates an IPv6 prefix (and corresponding route) to be configured for the user.",
    "defaultPriority":  "The default priority (of a process, print job, and so on).",
    "pager":  "The primary pager number.",
    "msDS-PasswordSettingsPrecedence":  "Password settings precedence.",
    "msDS-ServiceAccountDNSDomain":  "ADAM: The domain which the ADAM service account is a member of.",
    "msDS-OperationsForAzRoleBL":  "Backward link from Az-Operation to Az-Role objects that link to it.",
    "priorValue":  "The previous value for a secret.",
    "msDS-DisableForInstances":  "The set of DSA objects, which represent ADAM instances, for which Service Connection Point publication should be disabled.",
    "ipProtocolNumber":  "Contains the section of the protocols map that stores the unique number that identifies the protocol.",
    "mSMQOwnerID":  "The MSMQ-Owner-ID attribute contains MSMQ mixed-mode information.",
    "mS-SQL-Vines":  "The Vines connection point.",
    "msDS-GeoCoordinatesLongitude":  "Geo-coordinates for location services, specifically, longitude (in microdegrees) of the office or conference room.",
    "msSFU30SearchAttributes":  "Contains the names of the attributes the NIS server needs while searching a map.",
    "mSMQSiteID":  "The MSMQ-Site-ID attribute contains MSMQ mixed-mode information.",
    "msDS-PortSSL":  "Specifies which port is used by the directory service for SSL requests.",
    "initialAuthIncoming":  "Contains information about an initial incoming authentication request by a client to this server. This request is then sent by this server to the authentication server for the domain.",
    "rangeLower":  "The minimum value or length of an attribute.",
    "msDs-masteredBy":  "Backward link for msDS-hasMasterNCs.",
    "objectCategory":  "An object class name used to group objects of this or derived classes.",
    "proxyAddresses":  "A proxy address is the address by which a Microsoft Exchange Server recipient object is recognized in a foreign mail system. Proxy addresses are required for all recipient objects, such as custom recipients and distribution lists.",
    "privateKey":  "An encrypted private key.",
    "msTSConnectClientDrives":  "Terminal Services session Connect Client Drives specifies whether to reconnect to mapped client drives at logon.",
    "msWMI-Parm2":  "The ms-WMI-Parm2 attribute is reserved for internal use.",
    "mS-SQL-AllowAnonymousSubscription":  "True if the publication allows anonymous subscribers to subscribe to it.",
    "otherMailbox":  "Contains other additional mail addresses in a form such as CCMAIL: BruceKeever.",
    "dSASignature":  "The DSA-Signature of an object is the Invocation-ID of the last directory to modify the object.",
    "msDFSR-RootPath":  "Contains the full path of the root directory.",
    "displayNamePrintable":  "The printable display name for an object. The printable display name is usually the combination of the user\u0027s first name, middle initial, and last name.",
    "rpcNsObjectID":  "The Object IDs exported by a given server.",
    "mobile":  "The primary mobile phone number.",
    "msTSExpireDate3":  "Expiration date of the third TS per user CAL.",
    "msSFU30MapFilter":  "Contains a string used to filter data in a map. Can contain map keys, the domain name, and other types of data.",
    "schemaUpdate":  "The Schema-Update attribute is not currently used.",
    "msDS-SPNSuffixes":  "This attribute describes the suffixes of DNS host names used by servers in the forest. These DNS suffixes will be shared with other forests that have cross-forest trust with this forest.",
    "mSMQNt4Stub":  "The MSMQ-Nt4-Stub attribute contains MSMQ mixed-mode information.",
    "aCSTimeOfDay":  "Times of day at which this policy applies.",
    "msWMI-ClassDefinition":  "Holds a class definition to be enstated in some WMI namespace.",
    "printFormName":  "The name of the currently loaded form.",
    "msDS-TDOIngressBL":  "Backlink to TDO Ingress rules link on object.",
    "attributeSyntax":  "The OID for the syntax for this attribute.",
    "msKds-PrivateKeyLength":  "The length of the secret agreement private key.",
    "msDS-ValueTypeReferenceBL":  "This is the back link for ms-DS-Value-Type-Reference. It links a value type object back to resource properties.",
    "msDS-PerUserTrustQuota":  "Used to enforce a per-user quota for creating Trusted-Domain objects that are authorized by the new control access right, Create-Inbound-Forest-Trust. This attribute limits the number of Trusted-Domain objects that can be created by a single non-admin user.",
    "msDS-SeniorityIndex":  "Contains the seniority index as applied by the organization where the person works.",
    "uSNCreated":  "The update sequence number (USN) assigned at object creation. See also, USN-Changed.",
    "mS-SQL-ConnectionURL":  "The MS-SQL-ConnectionURL attribute is not currently used.",
    "msImaging-PSPIdentifier":  "Schema Attribute that contains the unique identifier for this Post Scan Process.",
    "msDS-OperationsForAzRole":  "List of operations linked to Az-Role.",
    "msDS-PrimaryComputer":  "For a user or group object, identifies the primary computers.",
    "businessCategory":  "Descriptive text on an Organizational Unit.",
    "msWMI-SourceOrganization":  "The business organization that initially created a policy object.",
    "printMACAddress":  "The user-supplied MAC address.",
    "minTicketAge":  "This attribute determines the minimum time period, in hours, that a user\u0027s ticket-granting ticket (TGT) can be used for Kerberos authentication before a request can be made to renew the ticket.",
    "msKds-Version":  "Version number of this root key.",
    "msDNS-DNSKEYRecords":  "An attribute that contains the DNSKEY record set for the root of the DNS zone and the root key signing key signature records.",
    "otherIpPhone":  "The list of alternate TCP/IP addresses for the phone. Used by Telephony.",
    "ms-net-ieee-8023-GP-PolicyGUID":  "Contains a GUID that identifies a specific 802.3 Group Policy object on the domain.",
    "allowedChildClassesEffective":  "A list of classes that can be modified.",
    "oMObjectClass":  "The unique OID for the attribute or class.",
    "mS-SQL-Language":  "The language for the current instance of SQL Server.",
    "msDS-HasDomainNCs":  "Directory service replication information that details the domain naming contexts present on a particular server.",
    "msiScriptName":  "The name for the Microsoft installer advertisement script file for this application.",
    "msDS-PhoneticLastName":  "Contains the phonetic last name of the person.",
    "attributeTypes":  "A multi-valued property that contains strings that represent each attribute in the schema.",
    "aCSMaxTokenBucketPerFlow":  "The ACS-Max-Token-Bucket-Per-Flow attribute is for internal use only. Based on RFC2210.",
    "authenticationOptions":  "The authentication options used in ADSI to bind to directory services objects.",
    "msDS-PasswordComplexityEnabled":  "Password complexity status for user accounts.",
    "systemFlags":  "An integer value that contains flags that define additional properties of the class. See Remarks.",
    "msDS-Auxiliary-Classes":  "This attribute lists the auxiliary classes that have been dynamically attached to an object. This attribute is not associated with a class. It is automatically populated by the system.",
    "logonWorkstation":  "This attribute is not used. See the User-Workstations attribute.",
    "flatName":  "For Windows NT domains, the flat name is the NetBIOS name. For links with non Windows NT domains, the flat name is the identifying name of that domain, or it is NULL.",
    "machineWidePolicy":  "Used to replicate the user-extensible policy to the clients.",
    "userSMIMECertificate":  "Certificate distribution object or tagged certificates.",
    "privilegeAttributes":  "Bitmask of privilege attributes.",
    "partialAttributeSet":  "Tracks the internal replication state of partial replicas (that is, on GCs). Attribute of the partial replica NC object. Defines the set of attributes present on a particular partial replica NC.",
    "msDS-SupportedEncryptionTypes":  "The encryption algorithms supported by user, computer or trust accounts.",
    "printDuplexSupported":  "Indicates the type of duplex support a printer has.",
    "uniqueMember":  "The distinguished name for the member of a group. Used by groupOfUniqueNames.",
    "manager":  "Contains the distinguished name of the user who is the user\u0027s manager. The manager\u0027s user object contains a directReports property that contains references to all user objects that have their manager properties set to this distinguished name.",
    "repsTo":  "Lists the servers that the directory will notify of changes and servers to which the directory will send changes on Request for the defined naming context.",
    "meetingRating":  "The Platform for Internet Content Selection rating of a meeting.",
    "msTPM-OwnerInformation":  "Contains the owner information of a particular TPM.",
    "msDFSR-MinDurationCacheInMin":  "Minimum time, in minutes, before truncating files.",
    "msDNS-ParentHasSecureDelegation":  "An attribute used to define whether the parental delegation to the DNS zone is secure.",
    "msDS-TasksForAzRole":  "List of tasks for Az-Role.",
    "msDS-NeverRevealGroup":  "Used with RODCs to define which users, computers, and groups are not allowed to have their passwords cached on an RODC.",
    "mSMQRoutingService":  "Indicates whether this server provides routing services.",
    "logonHours":  "The hours that the user is allowed to logon to the domain.",
    "sIDHistory":  "Contains previous SIDs used for the object if the object was moved from another domain. Whenever an object is moved from one domain to another, a new SID is created and that new SID becomes the objectSID. The previous SID is added to the sIDHistory property.",
    "msDs-MaxValues":  "Max values allowed.",
    "msDFSR-ContentSetGuid":  "Contains the Distributed File System (DFS) Replication content set GUID.",
    "msDS-LockoutDuration":  "Lockout duration for locked out user accounts.",
    "msDS-AzGenerateAudits":  "A Boolean field that indicates whether runtime audits need to be turned on (include audits for access checks, and so on).",
    "isDeleted":  "If TRUE, this object has been marked for deletion and cannot be instantiated. After the tombstone period has expired, it will be removed from the system.",
    "msDNS-NSEC3HashAlgorithm":  "An attribute that defines the current NSEC3 salt string being used to sign the DNS zone.",
    "msSFU30MaxUidNumber":  "Contains the maximum number of users that can be migrated to an NIS domain.",
    "instanceType":  "A bitfield that dictates how the object is instantiated on a particular server. The value of this attribute can differ on different replicas even if the replicas are in sync.",
    "sDRightsEffective":  "This constructed attribute returns a single DWORD value that can have up to three bits set:",
    "aCSCacheTimeout":  "The amount of time before the expiration of ACS objects that are cached by the ACS service.",
    "otherLoginWorkstations":  "Non Windows NT or LAN Manager workstations from which a user can log on.",
    "ipsecDataType":  "The Ipsec-Data-Type attribute is for internal use only.",
    "proxyLifetime":  "Contains the lifetime for a proxy object.",
    "mSMQOutRoutingServers":  "DN links to MSMQ routing servers through which all outgoing traffic for this computer should be routed.",
    "defaultGroup":  "The group to which this object is assigned when it is created.",
    "msWMI-Query":  "A single WQL query.",
    "msDS-LastFailedInteractiveLogonTime":  "The time that an incorrect password was presented during a Ctrl+Alt+Delete logon.",
    "aCSMaxPeakBandwidth":  "The peak bandwidth that can be reserved.",
    "msDS-NC-RO-Replica-Locations-BL":  "Backward link attribute for ms-DS-NC-RO-Replica-Locations.",
    "unicodePwd":  "The password of the user in Windows NT one-way format (OWF). Windows 2000 uses the Windows NT OWF. This property is used only by the operating system. Note that you cannot derive the clear password back from the OWF form of the password.",
    "msWMI-Genus":  "Identifies the object type of an encoding.",
    "printStaplingSupported":  "TRUE if the printer supports stapling. Supplied by the driver.",
    "printNumberUp":  "The number of page images per sheet.",
    "legacyExchangeDN":  "The distinguished name previously used by Exchange.",
    "msDFSR-DeletedPath":  "Full path of the Deleted directory.",
    "creator":  "The person that created the object.",
    "msDFS-NamespaceIdentityGUIDv2":  "To be set only when the namespace is created. Stable across rename or move as long as the namespace is not replaced by another namespace that has same name.",
    "msImaging-PSPString":  "Schema Attribute that contains the XML sequence for this Post Scan Process.",
    "oEMInformation":  "For holding OEM information. No longer used. Here for backward compatibility.",
    "originalDisplayTableMSDOS":  "The MAPI (original) display table for an MSDOS address entry.",
    "queryPoint":  "The URL or UNC of a query page or other front end for accessing a catalog.",
    "msPKI-Cert-Template-OID":  "Specifies the object identifier for a certificate template.",
    "meetingType":  "The type of meeting being held.",
    "appSchemaVersion":  "This attribute stores the schema version of the class store. It is used to provide correct behavior across schema changes.",
    "msPKI-Template-Schema-Version":  "Keeps track of schema updates of the PKI-Certificate-Template class.",
    "nonSecurityMemberBL":  "List of nonsecurity-members for an Exchange distribution list.",
    "msSFU30IsValidContainer":  "Contains internal data that is used by the server for NIS, which stores whether the current search root is valid.",
    "uSNDSALastObjRemoved":  "Contains the update sequence number (USN) for the last system object that was removed from a server.",
    "msDS-BridgeHeadServersUsed":  "List of bridge head servers used by KCC in the previous run.",
    "msTSPrimaryDesktopBL":  "This attribute represents the backward link to a user.",
    "mS-SQL-AllowImmediateUpdatingSubscription":  "True if the publication allows synchronized transaction updating subscriptions.",
    "fromEntry":  "This is a constructed attribute that is TRUE if the object is writable and FALSE if it is read-only, for example, a GC replica instance.",
    "trustAuthOutgoing":  "Authentication information for the outgoing portion of a trust.",
    "mSMQSite2":  "The DN of the second site of a pair that are connected.",
    "retiredReplDSASignatures":  "Tracks the past DS replication identities of a given DC.",
    "fRSControlInboundBacklog":  "Warning/Error level pair for inbound backlog (number of files).",
    "initials":  "Contains the initials for parts of the user\u0027s full name. This may be used as the middle initial in the Windows Address Book.",
    "msDFSR-RdcEnabled":  "Contains the value that specifies if RDC is enabled or disabled.",
    "mSMQLabel":  "Replaced by MSMQ-Label-Ex.",
    "mS-SQL-Database":  "The name of the SQL Server database involved in replication.",
    "mS-SQL-Version":  "The version for the current instance of SQL Server.",
    "msDS-IsPossibleValuesPresent":  "This attribute identifies if ms-DS-Claim-Possible-Values on linked resource property must have value or must not have value.",
    "shortServerName":  "Pre-Windows 2000 compatible server name for print servers.",
    "netbootServer":  "The distinguished name of a NetBoot server.",
    "volTableGUID":  "The unique identifier for a Link-Track-Volume table entry.",
    "creationWizard":  "Wizard to activate when creating objects of this class.",
    "msFVE-RecoveryGuid":  "Contains the GUID associated with a Full Volume Encryption (FVE) recovery password.",
    "meetingBlob":  "The opaque representation of a meeting in external form.",
    "adminCount":  "Indicates that a given object has had its ACLs changed to a more secure value by the system because it was a member of one of the administrative groups (directly or transitively).",
    "aCSServiceType":  "The ACS service type. Controlled load or guaranteed bandwidth.",
    "ntPwdHistory":  "The password history of the user in Windows NT one-way format (OWF). Windows 2000 uses the Windows NT OWF.",
    "displayName":  "The display name for an object. This is usually the combination of the users first name, middle initial, and last name.",
    "rpcNsCodeset":  "The list of character sets supported by the server.",
    "msDFS-SchemaMajorVersion":  "Contains the major version of the schema of DFS metadata.",
    "thumbnailLogo":  "BLOB that contains a logo for this object.",
    "msPKI-Template-Minor-Revision":  "Keeps track of attributes in the class changing. However, this will not trigger auto-enrollment.",
    "msDNS-SignatureInceptionOffset":  "An attribute that defines in seconds how far in the past DNSSEC signature validity periods should begin when signing the DNS zone.",
    "mSMQJournal":  "Indicates whether messages retrieved form the queue should be kept in journal.",
    "dITContentRules":  "This specifies the permissible content of entries of a particular structural object class by using the identification of an optional set of auxiliary object classes, and mandatory, optional, and precluded attributes. Collective attributes are included in DIT-Content-Rules as defined in RFC 2251.",
    "msTSEndpointData":  "This attribute represents the VM Name for a computer in a TSV deployment.",
    "msTSProfilePath":  "Terminal Services Profile Path specifies a roaming or mandatory profile path to use when the user logs on to the terminal server. The profile path is in the following network path format: **\\\\ServerName\\ProfilesFolderName\\**UserName.",
    "dSCorePropagationData":  "The DS-Core-Propagation-Data attribute is for internal use only.",
    "msDS-AdditionalDnsHostName":  "The attribute is used to store the additional DNS host name of a computer object. This attribute is used at the time a computer is renamed, or names are managed with \"netdom computername\".",
    "mS-SQL-Clustered":  "True if the server is clustered.",
    "msDNS-PropagationTime":  "An attribute used to define in seconds the expected time required to propagate zone changes through Active Directory.",
    "mSMQDsService":  "Indicates whether this server provides DS services.",
    "ms-DS-MachineAccountQuota":  "The number of computer accounts that a user is allowed to create in a domain.",
    "mS-DS-CreatorSID":  "The security ID of the creator of the object that contains this attribute.",
    "dhcpFlags":  "The dhcp-Flags attribute is not currently used.",
    "meetingID":  "The ID for the meeting.",
    "ms-DS-UserAccountAutoLocked":  "Indicates whether the account that this attribute references has been locked out. True if the account is locked out; otherwise, False.",
    "msFRS-Topology-Pref":  "The ms-FRS-Topology-Pref attribute is used to record the preferred NTFRS topology settings. When an FRS member gets added or deleted to the replica set, these attributes are referred, and adjustments made to the connections between the rest of the FRS members in the replica set.",
    "msDNS-IsSigned":  "An attribute used to define whether or not the DNS zone is signed.",
    "trustAuthIncoming":  "Authentication information for the incoming portion of a trust.",
    "notificationList":  "The Notification-List attribute is not currently used.",
    "priority":  "The current priority (of a process, print job, and so on).",
    "msDS-RequiredForestBehaviorVersion":  "Required forest function level for this feature.",
    "tokenGroupsNoGCAcceptable":  "This attribute contains the list of SIDs due to a transitive group membership expansion operation on a given user or computer. Token groups cannot be retrieved if a Global Catalog is not present to retrieve the transitive reverse memberships.",
    "lastLogon":  "The last time the user logged on. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of zero means that the last logon time is unknown.",
    "rDNAttID":  "The RDN for the attribute that is used to name a class.",
    "mSMQRoutingServices":  "Indicates whether the MSMQ installed on this computer provides MSMQ routing services.",
    "msDS-MembersOfResourcePropertyListBL":  "Backlink for ms-DS-Members-Of-Resource-Property-List. For a resource property object, this attribute references the resource property list object that it is a member of.",
    "localPolicyReference":  "Distinguished Name of a local policy object that a policy object copies from.",
    "mSMQSignCertificatesMig":  "In MSMQ mixed-mode, the attribute contains the previous value of mSMQSignCertificates. MSMQ supports migration from the MSMQ 1.0 DS to the Windows 2000 DS, and mixed mode specifies a state in which some of the DS severs were not upgraded to Windows 2000.",
    "msWMI-StringDefault":  "Default string setting for a set of string parameter objects.",
    "desktopProfile":  "The location of the desktop profile for a user or group of users. Not used.",
    "msDS-AppliesToResourceTypes":  "For a resource property, this attribute indicates what resource types this resource property applies to.",
    "msSFU30Aliases":  "Contains part of the NIS mail map.",
    "msDS-PortLDAP":  "Specifies which port is used by the directory service to listen for LDAP requests.",
    "mS-SQL-LastDiagnosticDate":  "The last date that DBCC checkdb was run.",
    "systemMustContain":  "The list of mandatory attributes for a class. These attributes must be specified when an instance of the class is created. The list of attributes can only be modified by the system.",
    "nameServiceFlags":  "Configuration flags for RPC Name Service.",
    "mS-SQL-AppleTalk":  "The AppleTalk connection point.",
    "msTSDefaultToMainPrinter":  "Terminal Services Default To Main Printer specifies whether to print automatically to the client\u0027s default printer.",
    "msDS-AzApplicationData":  "A string that is used by individual applications to store whatever information they may need to.",
    "aCSMinimumLatency":  "The ACS-Minimum-Latency attribute is for internal use only. Based on RFC2210.",
    "mSMQCost":  "The cost of routing between two sites.",
    "printAttributes":  "A bitmask of printer attributes.",
    "purportedSearch":  "The search argument for an address book view.",
    "msDFSR-StagingSizeInMb":  "Contains the size, in megabytes, of the staging directory.",
    "fRSFileFilter":  "A list of file name extensions excluded from file replication.",
    "memberNisNetgroup":  "Contains the list of netgroups that are members of this netgroup.",
    "msDS-hasMasterNCs":  "Contains the naming contexts held by a domain controller. This attribute deprecates has-Master-NCs.",
    "queryPolicyObject":  "Reference to the default Query-Policy in force for this server.",
    "host":  "Specifies a host computer.",
    "domainCrossRef":  "This is a reference from a trusted domain object to the cross reference object of the trusted domain.",
    "msRADIUS-SavedFramedIpv6Prefix":  "Indicates an IPv6 prefix (and corresponding route) to be configured for the user.",
    "msCOM-UserLink":  "A link used to associate a COM+ PartitionSet with a User object.",
    "msSFU30ResultAttributes":  "Contains an object that the NIS server uses for temporary storage.",
    "msTPM-SrkPubThumbprint":  "This attribute contains the thumbprint of the SrkPub corresponding to a particular TPM. This helps to index the TPM devices in the directory.",
    "msSFU30YpServers":  "Contains a list of NIS servers in an NIS domain.",
    "driverVersion":  "The Version number of device driver.",
    "defaultSecurityDescriptor":  "The security descriptor to be assigned to the object when it is first created.",
    "msDS-LastSuccessfulInteractiveLogonTime":  "The time that the correct password was presented during a Ctrl+Alt+Delete logon.",
    "defaultClassStore":  "The default Class Store for a given user.",
    "msDS-FailedInteractiveLogonCountAtLastSuccessfulLogon":  "The total number of failed interactive logons up until the last successful C-A-D logon.",
    "uSNSource":  "Value of the USN-Changed attribute of the object from the remote directory that replicated the change to the local server.",
    "previousParentCA":  "Reference to the certification authorities that issued the last expired certificate for a certification authority.",
    "meetingProtocol":  "The protocols used for a meeting, for example, H.320 or T.120.",
    "msDFS-TargetListv2":  "Targets that correspond to the DFS root or link.",
    "mSMQAuthenticate":  "Indicates whether the queue accepts only authenticated messages.",
    "pKIDefaultCSPs":  "The list of cryptographic service providers for the certificate template.",
    "homePostalAddress":  "A user\u0027s home address.",
    "mS-SQL-LastBackupDate":  "The last date that the database was backed up.",
    "whenCreated":  "The date when this object was created. This value is replicated and is in the global catalog.",
    "msAuthz-EffectiveSecurityPolicy":  "For a central access rule, this attribute defines the permission that is applying to the target resources on the central access rule.",
    "rIDAvailablePool":  "The space from which RID Pools are allocated.",
    "catalogs":  "The list of catalogs that index storage on a given computer.",
    "msPKIRoamingTimeStamp":  "Time stamp for last change to roaming tokens.",
    "msDS-UpdateScript":  "This is used to hold the script with the domain restructure instructions.",
    "msSPP-ConfirmationId":  "Confirmation ID (CID) used for phone activation of the Active Directory forest",
    "msDS-NCReplCursors":  "A list of past and present replication partners, and how current we are with each of them.",
    "msDS-LockoutObservationWindow":  "The period of time that a user account will be locked out.",
    "msDS-SDReferenceDomain":  "The name of the domain to be used for security descriptor translation for a non-domain-naming context.",
    "ipsecNFAReference":  "The Ipsec-NFA-Reference attribute is for internal use only.",
    "badPwdCount":  "The number of times the user tried to log on to the account using an incorrect password. A value of 0 indicates that the value is unknown.",
    "queryFilter":  "Query-filter attribute.",
    "superScopes":  "This attribute is used to group together all the different scopes used in the DHCP class into a single entity.",
    "modifiedCountAtLastProm":  "The Net Logon Change Log serial number at last promotion.",
    "msDS-ReplAuthenticationMode":  "The ms-DS-Repl-Authentication-Mode attribute is used to specify which authentication method is used to authenticate replication partners. This attribute applies to the configuration partition of an ADAM instance.",
    "msFVE-VolumeGuid":  "Contains the GUID associated with a BitLocker-supported disk volume.",
    "msDS-GroupMSAMembership":  "This attribute is used for access checks to determine if a requestor has permission to retrieve the password for a group MSA.",
    "msExchAssistantName":  "Contains the name of the assistant for an account.",
    "optionDescription":  "This attribute contains a description of an option that is set on the DHCP server.",
    "msWMI-Int8ValidValues":  "The valid values for a WMI 64-bit integer parameter object.",
    "operatingSystem":  "The Operating System name, for example, Windows Vista Enterprise.",
    "flags":  "To be used by the object to store bit information.",
    "aCSEnableRSVPMessageLogging":  "True if RSVP logging is enabled.",
    "gPCUserExtensionNames":  "Used by the Group Policy Object for user policies.",
    "mSMQUserSid":  "The migrated user\u0027s SID.",
    "msDFSR-StagingPath":  "Contains the full path of the staging directory.",
    "msTSSecondaryDesktops":  "This attribute represents the array of forward links to a user\u0027s secondary desktops.",
    "msWMI-TargetType":  "WMI reference to a type definition for a policy object.",
    "printMemory":  "The amount of memory installed in a printer.",
    "msWMI-CreationDate":  "The date and time stamp of an object instance\u0027s creation.",
    "extendedAttributeInfo":  "A multi-valued property that contains strings that represent additional information for each attribute.",
    "msDS-Preferred-GC-Site":  "The ms-DS-Preferred-GC-Site attribute is used by the Security Accounts Manager for group expansion during token evaluation.",
    "assocNTAccount":  "The Windows\u0026nbsp;NT account that applies to this object.",
    "msDS-IntId":  "The ms-DS-IntId attribute is for internal use only.",
    "msWMI-IntMin":  "The minimum value for a WMI 32-bit integer parameter object.",
    "domainID":  "Reference to a domain that is associated with a certification authority.",
    "ipsecNegotiationPolicyReference":  "The Ipsec-Negotiation-Policy-Reference attribute is for internal use only.",
    "mSMQQMID":  "The objectGUID of the server\u0027s MSMQ-configuration object.",
    "msWMI-TargetPath":  "List of key/value pairs for uniquely identifying a WMI object.",
    "uASCompat":  "Indicates if the security account manager will enforce data sizes to make Active Directory compatible with the LanManager User Account System (UAS). If this value is 0, no limits are enforced. If this value is 1, the following limits are enforced.",
    "rIDNextRID":  "The next free Relative Identifier in the current pool.",
    "userPrincipalName":  "This attribute contains the UPN that is an Internet-style login name for a user based on the Internet standard RFC 822. The UPN is shorter than the distinguished name and easier to remember. By convention, this should map to the user email name. The value set for this attribute is equal to the length of the user\u0027s ID and the domain name. For more information about this attribute, see User Naming Attributes.",
    "oMTGuid":  "The unique identifier for a Link-Track-Object-Move table entry.",
    "printMaxCopies":  "The maximum number of copies a device can print.",
    "dSUIAdminMaximum":  "This is the default maximum number of objects to be shown in a container by the admin UI.",
    "aCSMaxPeakBandwidthPerFlow":  "The peak bandwidth any flow can consume.",
    "aCSDirection":  "Send, Receive, Send/Receive, or None.",
    "msDS-ExecuteScriptPassword":  "Used during domain rename. This value cannot be written to or read from by using LDAP.",
    "aCSRSVPLogFilesLocation":  "The file system path for storage of RSVP log files.",
    "msDNS-DSRecordAlgorithms":  "An attribute used to define the algorithms used when writing the dsset file during zone signing.",
    "msDS-ReplAttributeMetaData":  "A list of metadata for each replicated attribute. The metadata indicates who changed the attribute last.",
    "ipNetworkNumber":  "Contains an IP network number in dotted decimal notation, omitting the leading zeros.",
    "ipsecName":  "The Ipsec-Name attribute is for internal use only.",
    "pKIExtendedKeyUsage":  "The enhanced key usage OIDs for the certificate template.",
    "msDS-TDOEgressBL":  "Backlink to TDO Egress rules link on object.",
    "gPOptions":  "Options that affect all group policies associated with the object hosting this property.",
    "cRLPartitionedRevocationList":  "Public Key Infrastructure revocation lists.",
    "countryCode":  "Specifies the country/region code for the user\u0027s language of choice. This value is not used by Windows 2000.",
    "aCSEnableRSVPAccounting":  "True if RSVP accounting is enabled.",
    "msNPSavedCallingStationID":  "The msNPSavedCallingStationID attribute is used internally. Do not modify this value directly.",
    "accountNameHistory":  "The length of time that the account has been active.",
    "msDS-KrbTgtLinkBl":  "Backward link for the ms-DS-KrbTgt-Link attribute.",
    "msWMI-TargetObject":  "This holds one or more binary sequences that represent compiled WMI objects.",
    "policyReplicationFlags":  "Determines which LSA properties are replicated to clients.",
    "aCSAggregateTokenRatePerUser":  "The maximum token rate any user may have for all flows.",
    "netbootMaxClients":  "The netboot-Max-Clients attribute is reserved for internal use.",
    "mSMQSiteGatesMig":  "In MSMQ mixed-mode, the previous value of mSMQSiteGates.",
    "msDS-AuthenticatedToAccountlist":  "Backward link for ms-DS-AuthenticatedAt-DC. Identifies which users have authenticated to this Computer.",
    "builtinModifiedCount":  "The Builtin-Modified-Count attribute is used to support replication to Windows NT 4.0 domains.",
    "msDS-IngressClaimsTransformationPolicy":  "This is a link to a Claims Transformation Policy Object for the ingress claims (claims entering this forest) from the Trusted Domain. This is applicable only for an outgoing or bidirectional Cross-Forest Trust. If this link is absent, all the ingress claims are dropped.",
    "sn":  "This attribute contains the family or last name for a user.",
    "msSFU30PosixMemberOf":  "Contains the display names of groups to which this user belongs.",
    "owner":  "The distinguished name of an object that has ownership of an object.",
    "aCSDSBMDeadTime":  "This attribute contains the election dead time interval (DSBMDeadInterval) for a domain. If the Designated Subnet Bandwidth Manager does not send out an I_AM_DSBM advertisement during this interval, then the other Subnet Bandwidth Managers (SBMs) in the domain elect a new DSBM.",
    "co":  "The country/region in which the user is located.",
    "msDS-PhoneticDisplayName":  "The phonetic display name of an object. In the absence of a phonetic display name, the existing display name is used.",
    "msDS-PSOApplied":  "Password settings object that is applied to this object.",
    "mS-SQL-ThirdParty":  "This attribute indicates whether the publication data is from a data source other than SQL Server. If it is from another source, then it is set to TRUE.",
    "setupCommand":  "This attribute indicates whether a setup command is required to set up this application.",
    "msiScriptSize":  "The file size for the Microsoft installer advertisement script file for this application.",
    "unstructuredName":  "The DNS name of the router (for example: router1.fabrikam.com).",
    "msTSManagingLS3":  "Issuer name of the third terminal server per user CAL.",
    "primaryGroupToken":  "A computed attribute that is used in retrieving the membership list of a group, such as Domain Users. The complete membership of such groups is not stored explicitly for scaling reasons.",
    "implementedCategories":  "This attribute contains a list of component category IDs that this object implements.",
    "shadowExpire":  "Contains the absolute date to expire the account.",
    "msWMI-IntMax":  "The maximum value for a WMI 32-bit integer parameter object.",
    "currentParentCA":  "Reference to the certification authorities that issued the current certificates for a certification authority.",
    "msImaging-HashAlgorithm":  "Contains the name of the hash algorithm used to create the Thumbprint Hash for the Scan Repository/Secure Print Device.",
    "msDS-IsPrimaryComputerFor":  "Backlink attribute for msDS-IsPrimaryComputer.",
    "remoteStorageGUID":  "This attribute contains the GUID for a remote storage object.",
    "userPassword":  "The user\u0027s password in UTF-8 format. This is a write-only attribute.",
    "msDS-RequiredDomainBehaviorVersion":  "Required domain function level for this feature.",
    "syncWithObject":  "Distinguished name of the object being synchronized for the SAM builtin group/local policy synchronization.",
    "objectCount":  "Tracked file quota for a given volume.",
    "msDFSR-MaxAgeInCacheInMin":  "Maximum time, in minutes, to keep files in full form.",
    "mSMQQueueJournalQuota":  "The journal message quota of the queue.",
    "lmPwdHistory":  "The password history of the user in LAN Manager (LM) one-way format (OWF). The LM OWF is used for compatibility with LAN Manager 2.x clients, Windows 95, and Windows 98.",
    "whenChanged":  "The date when this object was last changed. This value is not replicated and exists in the global catalog.",
    "msDS-Replication-Notify-Subsequent-DSA-Delay":  "This attribute controls the delay in time between notification of each subsequent replica partner for an NC.",
    "helpFileName":  "This attribute was used for Exchange 4.0. It contained the name that should be used for the file when the provider downloaded help data to a client computer. It is not used for any other versions of Exchange.",
    "privilegeHolder":  "List of Distinguished Names of principals granted this privilege.",
    "msDS-TrustForestTrustInfo":  "Contains forest trust information (a binary BLOB) that is used by the system for a Trusted-Domain object.",
    "mSMQNameStyle":  "The convention of computer name style.",
    "adminMultiselectPropertyPages":  "This is a multivalued attribute whose values are a number that represents the order in which the pages are added and a GUID of a COM object that implements multi-select property pages for the AD Users and Computers snap-in.",
    "mSMQQueueQuota":  "A message quota of the queue.",
    "msWMI-IntValidValues":  "The valid values for a WMI 32-bit integer parameter object.",
    "printStatus":  "Status from the print spooler. Currently unused.",
    "physicalLocationObject":  "Used to map a device (for example, a printer, computer, and so on) to a physical location.",
    "msDS-AllUsersTrustQuota":  "Used to enforce a combined users quota on the total number of Trusted-Domain objects created by using the new control access right, Create-Inbound-Forest-Trust.",
    "msTSEndpointType":  "This attribute defines whether the computer is a physical computer or a virtual machine.",
    "netbootLocallyInstalledOSes":  "The netboot-Locally-Installed-OSes attribute is reserved for internal use.",
    "mhsORAddress":  "X.400 address.",
    "mSMQQuota":  "The MSMQ messages quota for the computer.",
    "homeDirectory":  "The home directory for the account. If homeDrive is set and specifies a drive letter, homeDirectory must be a UNC path. Otherwise, homeDirectory is a fully qualified local path including the drive letter (for example, DriveLetter**:\\Directory\\**Folder). This value can be a null string.",
    "msDS-UserPasswordExpiryTimeComputed":  "Contains the expiry time for the user\u0027s current password.",
    "msDS-OptionalFeatureGUID":  "GUID of an optional feature.",
    "isCriticalSystemObject":  "If TRUE, the object hosting this attribute must be replicated during installation of a new replica.",
    "msMQ-Recipient-FormatName":  "Used as the recipient format name in the MSMQ-Custom-Recipient class.",
    "localeID":  "This attribute contains a list of locale IDs supported by this application. A locale ID represents a geographic location, such as a country/region, city, county, and so on.",
    "msDS-ReplValueMetaData":  "A list of metadata for each value of an attribute. The metadata indicates who changed the value last.",
    "nonSecurityMember":  "Nonsecurity members of a group. Used for Exchange distribution lists.",
    "msDS-HABSeniorityIndex":  "Contains the seniority index as applied by the organization where the person works.",
    "otherFacsimileTelephoneNumber":  "A list of alternate facsimile numbers.",
    "msDS-AzScriptEngineCacheMax":  "The maximum number of scripts that are cached by the application.",
    "msFVE-RecoveryPassword":  "Contains the password required to recover a Full Volume Encryption (FVE) volume.",
    "msDFSR-DefaultCompressionExclusionFilter":  "Filter string that contains extensions of file types not to be compressed.",
    "msDFSR-DeletedSizeInMb":  "Size, in megabytes, of the deleted directory.",
    "systemPossSuperiors":  "The list of classes that can contain this class. This list can only be modified by the system.",
    "msDFSR-DfsLinkTarget":  "Contains the link target for the subscription.",
    "lastContentIndexed":  "The time this volume was last content indexed.",
    "msPKI-Certificate-Policy":  "Contains the list of policy OIDs and their optional CSPs in the issued certificate.",
    "meetingName":  "The name of the meeting.",
    "certificateTemplates":  "Contains information for a certificate issued by a Certificate Server.",
    "nisMapEntry":  "Contains one map entry for a nonstandard map.",
    "serviceClassID":  "The GUID for the Service class.",
    "netbootIntelliMirrorOSes":  "The netboot-IntelliMirror-OSes attribute is reserved for internal use.",
    "mSMQInterval2":  "In MSMQ mixed-mode, default replication time between sites.",
    "rpcNsInterfaceID":  "An Interface ID supported by a given server.",
    "objectClass":  "The list of classes from which this class is derived.",
    "msDS-OIDToGroupLinkBl":  "Backward link for ms-DS-OIDToGroup-Link. Identifies the issuance policy, represented by an OID object, that is mapped to this group.",
    "meetingRecurrence":  "The recurrence parameters for the meeting.",
    "transportDLLName":  "The name of the DLL that will manage a transport.",
    "mS-SQL-PublicationURL":  "The MS-SQL-PublicationURL attribute is not currently used.",
    "ms-net-ieee-80211-GP-PolicyGUID":  "Contains a GUID that identifies a specific 802.11 Group Policy object on the domain.",
    "treatAsLeaf":  "Display-specifiers with this flag set to TRUE force the related class to be displayed as a leaf class even if it has children.",
    "rpcNsAnnotation":  "A string that describs a given RPC Profile element.",
    "meetingEndTime":  "The date and time that the meeting is to end.",
    "teletexTerminalIdentifier":  "Specifies the Teletex terminal identifier and, optionally, parameters, for a teletex terminal associated with an object.",
    "scopeFlags":  "The Scope-Flags attribute is not currently used.",
    "msDNS-SignWithNSEC3":  "An attribute used to define whether or not the DNS zone is signed with NSEC3.",
    "linkID":  "An integer that indicates that the attribute is a linked attribute. An even integer is a forward link and an odd integer is a backward link.",
    "msCOM-ObjectId":  "GUID used to identify a COM+ Partition or a COM+ PartitionSet.",
    "shadowMin":  "Contains the minimum number of days between shadow changes.",
    "lSACreationTime":  "The LSA-Creation-Time attribute is used to support replication to Windows NT 4.0 domains.",
    "shellPropertyPages":  "The order number and GUID of property pages for managing Active Directory objects. These property pages can be accessed from the Windows shell. For more information see the document, Extending the User Interface for Directory Objects.",
    "msTAPI-uid":  "Provides the name of a TAPI multicast conference. It is the naming attribute of the Rt-Conference class.",
    "pKICriticalExtensions":  "The list of critical extensions in the certificate template.",
    "packageType":  "This attribute describes the type of installation required for an application package, for example, MSI, EXE, CAB.",
    "msDFSR-Version":  "Contains the version number of the Distributed File System (DFS) Replication service.",
    "msTSInitialProgram":  "Terminal Services Session Initial Program specifies the path and file name of the application that the user wants to start automatically when the user logs on to the Terminal Server.",
    "msSPP-KMSIds":  "KMS IDs enabled by the Activation Object",
    "fRSDirectoryFilter":  "The list of directories excluded from file replication, for example, temp, or obj.",
    "msTSLicenseVersion4":  "Version of the fourth terminal server per user CAL.",
    "mSMQDependentClientServices":  "Indicates whether the MSMQ installed on this computer provides MSMQ dependent client services.",
    "serviceDNSNameType":  "Type of DNS Record to look up for this service. For example, A or SRV.",
    "addressBookRoots2":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "requiredCategories":  "This attribute contains a list of component category IDs an object (such as an application) requires to run.",
    "siteGUID":  "The unique identifier for a site.",
    "msDFSR-DirectoryFilter":  "Contains the filter string that is applied to directories.",
    "mSMQQueueNameExt":  "Contains the suffix of the queue name (if there is one).",
    "volTableIdxGUID":  "The index identifier for a Link-Track-Volume table entry.",
    "msDS-AzGenericData":  "Generic AzMan-specific data.",
    "fRSMemberReference":  "Reference to member object for this subscriber.",
    "fRSReplicaSetType":  "This is a code that indicates whether this is a sysvol replica set, a DFS replica set, or other replica set.",
    "msRRASVendorAttributeEntry":  "A string that enables vendors to add router attributes to the DS for use in queries, in the form AttributeName:vendorID:AttributeType.",
    "msDS-ClaimPossibleValues":  "For a claim type or resource property object, this attribute describes the values suggested to a user when the he/she use the claim type or resource property in applications.",
    "proxyGenerationEnabled":  "TRUE if proxy generation is enabled.",
    "dNSProperty":  "Used to store binary settings (properties) on DNS zone objects.",
    "localPolicyFlags":  "Flags that determine where a computer gets its policy. Local-Policy-Reference.",
    "shadowWarning":  "Contains the number of days before the password expires to warn the user.",
    "msWMI-StringValidValues":  "The set of strings that belong to a string set parameter object.",
    "ms-net-ieee-80211-GP-PolicyReserved":  "Reserved for future use.",
    "mS-SQL-Contact":  "A user defined string. Default is set to Contact.",
    "msTSManagingLS2":  "Issuer name of the second terminal server per user CAL.",
    "dynamicLDAPServer":  "DNS name of server handing dynamic properties for this account.",
    "subClassOf":  "The parent class of a class.",
    "pKIKeyUsage":  "The key usage extension for the certificate template.",
    "netbootAllowNewClients":  "The netboot-Allow-New-Clients attribute is reserved for internal use.",
    "meetingIsEncrypted":  "This is TRUE if the meeting is to be encrypted.",
    "tokenGroups":  "A computed attribute that contains the list of SIDs due to a transitive group membership expansion operation on a given user or computer. Token Groups cannot be retrieved if no Global Catalog is present to retrieve the transitive reverse memberships.",
    "userCertificate":  "Contains the DER-encoded X.509v3 certificates issued to the user. Note that this property contains the public key certificates issued to this user by Microsoft Certificate Service.",
    "msTSAllowLogon":  "Specifies whether the user is allowed to log on to the Terminal Server. The value is 1 if logon is allowed, and 0 if logon is not allowed.",
    "iPSECNegotiationPolicyType":  "The Ipsec-Negotiation-Policy-Type attribute is for internal use only.",
    "msDS-RetiredReplNCSignatures":  "Information about naming contexts that are no longer held on this computer.",
    "msDS-LocalEffectiveDeletionTime":  "Deletion time of the object in the local DIT.",
    "validAccesses":  "The type of access that is permitted with an extended right.",
    "mSMQLongLived":  "The default time to live of MSMQ messages.",
    "msWMI-intFlags4":  "The ms-WMI-intFlags4 attribute is reserved for internal use.",
    "printerName":  "The display name of an attached printer.",
    "printLanguage":  "The supported page description language (for example, PostScript, PCL).",
    "controlAccessRights":  "Used by DS Security to determine which users can perform specific operations on the host object.",
    "msDS-NcType":  "A bitfield that maintains information about aspects of an NC replica that is relevant to replication.",
    "servicePrincipalName":  "List of principal names used for mutual authentication with an instance of a service on this computer.",
    "msSFU30KeyValues":  "Contains internal data that the NIS server uses to store temporary data.",
    "msSFU30Domains":  "Contains the list of UNIX NIS domains that have all been migrated to the same AD NIS domain.",
    "possibleInferiors":  "The list of objects that this object can contain.",
    "msWMI-Class":  "The name of a WMI Class object in an associated encoding (for example, Win32_ComputerSystem). The encoding can be either an instance or class object.",
    "msPKI-OID-User-Notice":  "The user notice for the enterprise issuer policy OID.",
    "msDS-TransformationRules":  "Specifies the Transformation Rules for Cross-Forest Claims Transformation.",
    "msDS-GenerationId":  "For virtual machine snapshot resuming detection. This attribute represents the VM Generation ID.",
    "msDS-ServiceAccount":  "The FPO that represents the ADAM service account.",
    "uid":  "A user ID.",
    "msTPM-TpmInformationForComputer":  "This attribute links a Computer object to a TPM object.",
    "fRSReplicaSetGUID":  "GUID that identifies an FRS Replica Set.",
    "secretary":  "Contains the distinguished name of the secretary for an account.",
    "mSMQSignKey":  "The computer\u0027s public signing key.",
    "departmentNumber":  "Identifies a department within an organization.",
    "meetingStartTime":  "The date and time that the meeting starts.",
    "domainIdentifier":  "Domain Sid that identifies the domain.",
    "msDNS-NSEC3RandomSaltLength":  "An attribute that defines the length in bytes of the random salt used when signing the DNS zone.",
    "oMTIndxGuid":  "The index identifier for a Link-Track-Object-Move table entry.",
    "roomNumber":  "The room number of an object.",
    "mS-SQL-Keywords":  "Not used. Default is set to Key 1, Key 2, Key 3, Key 4, and Key 5.",
    "msDFS-Commentv2":  "Comment associated with DFS root or link.",
    "mS-SQL-Alias":  "An alias used to connect to the database. Default is set to Alias.",
    "isSingleValued":  "If TRUE, this attribute can only store one value.",
    "meetingBandwidth":  "The available bandwidth for a meeting.",
    "fRSControlDataCreation":  "Warning/Error level pair for file data creation (megabytes per second).",
    "msTAPI-ConferenceBlob":  "A binary BLOB of data that describes various properties of a TAPI multicast conference. Its format and content are determined by the value of the attribute Protocol-Id in the same object. Typically, the data in this BLOB conforms to RFC2327.",
    "currentLocation":  "The computer location for an object that has moved.",
    "msRADIUSServiceType":  "The msRADIUSServiceType attribute is used internally. Do not modify this value directly.",
    "cOMClassID":  "This attribute stores the list of ClassIDs that are implemented in this application package.",
    "pKT":  "DFS Partition Knowledge Table. Describes the structure of a Distributed File System hierarchy.",
    "contextMenu":  "The order number and GUID of the context menu to be used for an object.",
    "creationTime":  "The date and time that the object was created.",
    "msDS-SiteName":  "Lists the site name that corresponds to the DC.",
    "mSMQServices":  "The type of service provided by MSMQ installed on this server.",
    "cn":  "The name that represents an object. Used to perform searches.",
    "msFVE-KeyPackage":  "Contains a volume\u0027s BitLocker encryption key secured by the corresponding recovery password.",
    "domainCAs":  "List of certification authorities for a given domain.",
    "homeDrive":  "Specifies the drive letter to which to map the UNC path specified by homeDirectory. The drive letter must be specified in the form DriveLetter**:** where DriveLetter is the letter of the drive to map. The DriveLetter must be a single, uppercase letter and the colon (:) is required.",
    "facsimileTelephoneNumber":  "Contains telephone number of the user\u0027s business fax machine.",
    "directReports":  "Contains the list of users that directly report to the user. The users listed as reports are those that have the property manager property set to this user. Each item in the list is a linked reference to the object that represents the user.",
    "msiScriptPath":  "The file path for the Microsoft installer advertisement script file for this application.",
    "msDS-hasFullReplicaNCs":  "Identifies the partitions held as full replicas.",
    "msTSRemoteControl":  "Terminal Services Remote Control specifies whether to allow remote observation or remote control of the user\u0027s Terminal Services session.",
    "gPCMachineExtensionNames":  "Used by the Group Policy Object for computer policies.",
    "msDS-Non-Security-Group-Extra-Classes":  "The common names of the nonstandard classes that can be added to a nonsecurity group through the Active Directory Users and Computers snap-in.",
    "parentGUID":  "This is a constructed attribute, invented to support the DirSync control. This attribute holds the objectGuid of an object\u0027s parent when replicating an object\u0027s creation, rename, or move.",
    "isDefunct":  "If TRUE, the class or attribute is no longer usable. Old versions of this object may exist, but new ones cannot be created.",
    "extraColumns":  "This is a multivalued attribute whose values consist of a 5 tuple: (attribute name), (column title), (default visibility (0,1)), (column width ( 1 for auto width)), 0 (reserved for future use must be zero). This value is used by the AD Users and Computers console.",
    "replInterval":  "The attribute of Site-Link objects that defines the interval, in minutes, between replication cycles between the sites in the Site-List. Must be a multiple of 15 minutes (the granularity of cross-site DS replication), a minimum of 15 minutes, and a maximum of 10,080 minutes (one week).",
    "mSMQSite1":  "The DN of the first site of a pair that are connected.",
    "location":  "The user\u0027s location, such as office number.",
    "dhcpObjName":  "The dhcp-Obj-Name attribute is not currently used.",
    "msDS-AzLastImportedBizRulePath":  "The last imported business rule path.",
    "aCSServerList":  "Contains the DNS names of Windows NT servers that are allowed to run ACS.",
    "printStartTime":  "The time a print queue begins servicing jobs.",
    "msDFS-Propertiesv2":  "Properties associated with DFS root or link.",
    "msTSWorkDirectory":  "Terminal Services session Work Directory specifies the working directory path for the user.",
    "msDS-ClaimValueType":  "For a claim type object, specifies the value type of the claims issued.",
    "msDFS-Ttlv2":  "TTL associated with the DFS root or link. For use at DFS referral time.",
    "enabledConnection":  "Indicates whether a connection is available for use.",
    "msDS-QuotaUsed":  "The current quota consumed by a security principal in the directory database.",
    "helpData32":  "This attribute was used for the Win32 help file format for Exchange 4.0. It is not used for any other versions of Exchange.",
    "msDS-ObjectReference":  "A link to the object that uses the data stored in the object that contains this attribute.",
    "aCSMaxSizeOfRSVPLogFile":  "The maximum size, in bytes, of an RSVP log file.",
    "cOMTypelibId":  "This attribute stores the list of type library IDs contained in this application package.",
    "msRADIUS-SavedFramedIpv6Route":  "Provides routing information to be configured for the user on the NAS.",
    "shadowMax":  "Contains the maximum number of days a password can be valid.",
    "indexedScopes":  "List of directory scopes (for example, C:\\ or D:) that are indexed.",
    "seeAlso":  "List of distinguished names that are related to an object.",
    "oMSyntax":  "A number that represents the OM type for the attribute.",
    "mAPIID":  "An integer by which MAPI clients identify this attribute.",
    "msDS-User-Account-Control-Computed":  "msDS-User-Account-Control-Computed is much like userAccountControl, but the attribute\u0027s value can contain additional bits that are not persisted. The computed bits include the following.",
    "msTSConnectPrinterDrives":  "Terminal Services session Connect Printer Drives specifies whether to reconnect to mapped client printers at logon.",
    "printMinXExtent":  "The minimum horizontal print region.",
    "fRSUpdateTimeout":  "The maximum time, in minutes, to wait to complete an update before giving up.",
    "masteredBy":  "Backward link for Has-Master-NCs attribute. The distinguished name for its NTDS Settings objects.",
    "dhcpReservations":  "The dhcp-Reservations attribute is not currently used.",
    "iconPath":  "Source for loading an icon.",
    "msDS-HostServiceAccountBL":  "Service Accounts backward link for linking computers that are associated with the service account.",
    "msSPP-OnlineLicense":  "License used during online activation of the Active Directory forest",
    "msSPP-IssuanceLicense":  "Issuance license used during online/phone activation of the Active Directory forest",
    "moveTreeState":  "This attribute contains state information for a directory tree that is being moved.",
    "msTSLSProperty01":  "Placeholder terminal server property 01.",
    "msDFSR-RootSizeInMb":  "Contains the size, in megabytes, of the root directory.",
    "msPKI-Certificate-Application-Policy":  "The application policy OID\u0027s in a certificate.",
    "allowedAttributes":  "Attributes that will be permitted to be assigned to a class.",
    "msDS-OIDToGroupLink":  "For an OID, identifies the group object that corresponds to the issuance policy represented by this OID.",
    "rpcNsPriority":  "The priority of a given RPC profile entry.",
    "msDNS-DSRecordSetTTL":  "An attribute that defines the time-to-live (TTL) value assigned to DS records when signing the DNS zone.",
    "msWMI-Mof":  "Holds a MOF definition of some WMI object.",
    "mS-SQL-AllowKnownPullSubscription":  "True if the publication allows registered subscribers to subscribe.",
    "postalAddress":  "The mailing address for the object.",
    "msPKI-RA-Policies":  "Contains the list of required policy OIDs from registration authorities who sign the enrollment request.",
    "rpcNsBindings":  "The list of RPC bindings for the current interface.",
    "mS-SQL-CreationDate":  "The date the database was created.",
    "msCOM-PartitionSetLink":  "A link used to associate a COM+ Partition with a COM+ PartitionSet object.",
    "uNCName":  "The universal naming convention name for shared volumes and printers.",
    "msDFS-ShortNameLinkPathv2":  "Short Name DFS link path relative to the DFS root target share (that is, without the server/domain and DFS namespace name components). Use forward slashes (/) instead of backslashes (\\), so that LDAP searches can be done without having to use escapes.",
    "description":  "Contains the description to display for an object. This value is restricted as single-valued for backward compatibility in some cases but is allowed to be multi-valued in others. See Remarks.",
    "dmdName":  "A name used to identify the schema partition. Not used by AD.",
    "msDNS-RFC5011KeyRollovers":  "An attribute that defines whether or not the DNS zone should be maintained using key rollover procedures defined in RFC 5011.",
    "printPagesPerMinute":  "Driver-supplied print rate in pages per minute.",
    "msSPP-CSVLKPid":  "ID of CSVLK product-key used to create the Activation Object",
    "gPLink":  "A sorted list of Group Policy options. Each option is a DWORD. Use of the UNICODE string is a convenience.",
    "pekList":  "List of password encryption keys.",
    "mSMQJournalQuota":  "MSMQ journal messages quota of the computer.",
    "msieee80211-Data":  "Stores a list of preferred network configurations in Group Policy for wireless networks.",
    "userSharedFolder":  "Specifies a UNC path to the user\u0027s shared documents folder. The path must be a network UNC path of the form **\\\\Server\\Share\\**Directory. This value can be a null string.",
    "msDS-LogonTimeSyncInterval":  "This attribute controls the granularity, in days, with which the last logon time for a user or computer, recorded in the lastLogonTimestamp attribute, is replicated to all DCs in a domain.",
    "msPKIDPAPIMasterKeys":  "Storage of encrypted DPAPI Master Keys for user.",
    "trustAttributes":  "This attribute stores the trust attributes for a trusted domain. Possible attribute values are as follows:",
    "msWMI-NormalizedClass":  "The name of a core WMI policy class.",
    "msRADIUS-FramedInterfaceId":  "Indicates the IPv6 interface identifier to be configured for the user.",
    "MSMQ-SecuredSource":  "This is part of an MSMQ object, it is set by calling API to MQCreateQueue or MQSetProperties. It controls whether MSMQ accepts messages only from a secured source (for example, https) for this queue.",
    "meetingAdvertiseScope":  "Indicates whether an entry should be advertised outside a corporate gateway or proxy.",
    "uidNumber":  "Contains an integer that uniquely identifies a user in an administrative domain.",
    "templateRoots":  "This attribute is used on the Exchange config container to indicate where the template containers are stored. This information is used by the Active Directory MAPI provider.",
    "comment":  "The user\u0027s comments.",
    "meetingMaxParticipants":  "The maximum number of participants for a meeting.",
    "userPKCS12":  "PKCS #12 PFX PDU for exchange of personal identity information.",
    "msNPCallingStationID":  "The msNPCallingStationID attribute is used internally. Do not modify this value directly.",
    "msDS-AzTaskIsRoleDefinition":  "A Boolean field which indicates whether AzTask is a classic task or a role definition.",
    "mS-SQL-AllowSnapshotFilesFTPDownloading":  "True if the publication allows snapshot files to be downloaded by using FTP.",
    "mscopeId":  "Indicates that there is a multicast scope on the specified DHCP server.",
    "groupPriority":  "The Group-Priority attribute is not currently used.",
    "msDFSR-TombstoneExpiryInMin":  "Contains the tombstone record lifetime, in minutes.",
    "applicationName":  "The name of the application.",
    "aCSMaxNoOfAccountFiles":  "The maximum number of RSVP account files.",
    "x500uniqueIdentifier":  "Used to distinguish between objects when a distinguished name has been reused. This is a different attribute type from both the uid and uniqueIdentifier types.",
    "mS-SQL-AllowQueuedUpdatingSubscription":  "True to allow queued transactions when updating subscriptions.",
    "msDS-OperationsForAzTask":  "List of operations linked to Az-Task.",
    "bridgeheadTransportList":  "Transports for which this server is a bridgehead.",
    "msDFS-SchemaMinorVersion":  "Contains the minor version of the schema of DFS metadata.",
    "dhcpObjDescription":  "The dhcp-Obj-Description attribute is not currently used.",
    "dMDLocation":  "The distinguished name to the schema partition.",
    "shellContextMenu":  "The order number and GUID of the context menu for this object.",
    "aCSPolicyName":  "String name of an ACS policy that applies to this user.",
    "msieee80211-DataType":  "Internally used data type for msieee80211-Data BLOB.",
    "mS-SQL-UnicodeSortOrder":  "The Unicode sort order for the current instance of SQL Server.",
    "siteLinkList":  "List of site links that are associated with this bridge.",
    "memberOf":  "The distinguished name of the groups to which this object belongs.",
    "aCSMaxNoOfLogFiles":  "The maximum number of RSVP log files.",
    "lastLogoff":  "This attribute is not used.",
    "aCSMaxSizeOfRSVPAccountFile":  "The maximum size, in bytes, of an RSVP account file.",
    "cOMCLSID":  "This attribute contains the GUID that is associated to this object class.",
    "msDS-EgressClaimsTransformationPolicy":  "This is a link to a Claims Transformation Policy Object for the egress claims (claims leaving this forest) to the Trusted Domain. This is applicable only for an incoming or bidirectional Cross-Forest Trust. When this link is not present, all claims are allowed to egress as-is.",
    "mSMQSignCertificates":  "This attribute contains a number of certificates. A user can generate a certificate per computer. For each certificate we also keep a digest.",
    "msDS-MinimumPasswordLength":  "Minimum length for user account passwords.",
    "bootParameter":  "Provides data that is needed to start a diskless client.",
    "syncMembership":  "List of members contained in a SAM builtin group for synchronization.",
    "msTSMaxConnectionTime":  "Terminal Services session Maximum Connection Time is the maximum amount of time, in minutes, of the Terminal Services session.",
    "documentVersion":  "The documentVersion attribute type specifies the version number of a document.",
    "mS-SQL-GPSHeight":  "The MS-SQL-GPSHeight attribute is not currently used.",
    "pKIMaxIssuingDepth":  "The maximum length of the certificate chain issued by the certificate.",
    "subRefs":  "List of subordinate references of a Naming Context.",
    "msDS-OperationsForAzTaskBL":  "Backward link from Az-Operation to Az-Task objects that link to it.",
    "shadowLastChange":  "Contains the last change of the shadow information.",
    "packageFlags":  "A bitfield that contains the deployment state flags for an application.",
    "userSharedFolderOther":  "Specifies a UNC path to the user\u0027s additional shared documents folder. The path must be a network UNC path of the form **\\\\Server\\Share\\**Directory. This value can be a null string.",
    "pwdHistoryLength":  "The number of old passwords to save.",
    "dSHeuristics":  "Contains global settings for the entire forest.",
    "wWWHomePage":  "A web page that is the primary landing page of a website.",
    "adminDescription":  "The description displayed on admin screens.",
    "fRSStagingPath":  "The path to the file replication staging area.",
    "mS-SQL-InformationURL":  "Not used. Default is set to Information.",
    "cOMProgID":  "This attribute stores the list of COM object program IDs that are implemented in this application package.",
    "defaultObjectCategory":  "The object category to use for the object if one is not specified.",
    "msDS-AdditionalSamAccountName":  "This attribute is used to store the SAM account names that correspond to the DNS host names in ms-DS-Additional-Dns-Host-Name.",
    "printSpooling":  "A string that represents the type of printer spooling.",
    "msExchLabeledURI":  "An Exchange URI followed by a label. The label is used to describe the resource to which the URI points and is intended as a human-readable name.",
    "msTSHomeDrive":  "Terminal Services Home Drive specifies a Home drive for the user.",
    "supportedApplicationContext":  "Specifies the object identifiers of application contexts that an OSI application supports.",
    "printCollate":  "TRUE if a printer has collating bins.",
    "msDS-isRODC":  "Shows whether a DC is an RODC.",
    "msDNS-NSEC3CurrentSalt":  "An attribute that defines the current NSEC3 salt string being used to sign the DNS zone.",
    "msDS-Cached-Membership-Time-Stamp":  "The ms-DS-Cached-Membership-Time-Stamp attribute is used by the Security Accounts Manager for group expansion during token evaluation.",
    "msKds-SecretAgreementParam":  "The parameters for the secret agreement algorithm.",
    "pendingCACertificates":  "The certificates that are about to become effective for this certification authority.",
    "msRASSavedCallbackNumber":  "The msRASSavedCallbackNumber attribute is used internally. Do not modify this value directly.",
    "eFSPolicy":  "The Encrypting File System Policy.",
    "rIDPreviousAllocationPool":  "The RID-Previous-Allocation-Pool attribute contains the pool of relative identifiers (RIDs) that a domain controller allocates from. This attribute is an eight-byte value that contains a pair of four-byte integers that represent the start and end values of the RID pool. The start value is in the lower four bytes and the end value is in the upper four bytes.",
    "categories":  "List of category IDs (GUIDs) for categories that apply to this application.",
    "msTSManagingLS4":  "Issuer name of the fourth terminal server per user CAL.",
    "msDFS-GenerationGUIDv2":  "Updated each time the entry that contains this attribute is modified.",
    "localizedDescription":  "The localization ID and Display Name for an object.",
    "attributeDisplayNames":  "The name to be displayed for this object.",
    "codePage":  "Specifies the code page for the user\u0027s language of choice. This value is not used by Windows 2000.",
    "repsFrom":  "Lists the servers from which the directory will accept changes for the defined naming context.",
    "aCSAllocableRSVPBandwidth":  "The maximum bandwidth that can be reserved.",
    "msDS-AzScriptTimeout":  "The maximum time, in milliseconds, to wait for a script to finish auditing a specific policy.",
    "groupAttributes":  "The Group-Attributes attribute is not currently used.",
    "name":  "The Relative Distinguished Name (RDN) of an object. An RDN is the relative portion of a distinguished name (DN), which uniquely identifies an LDAP object.",
    "otherWellKnownObjects":  "Contains a list of containers by GUID and Distinguished Name. This permits retrieving an object after it has been moved by using just the GUID and the domain name. Whenever the object is moved, the system automatically updates the Distinguished Name.",
    "msTSLicenseVersion3":  "Version of the third terminal server per user CAL.",
    "dhcpProperties":  "The dhcp-Properties attribute is not currently used.",
    "mSMQSiteForeign":  "A Boolean value that indicates whether it is a foreign MSMQ site.",
    "aCSIdentityName":  "This attribute contains the DN of a user or OU and is the identity of a user or a group of users to which this QoS policy applies.",
    "msTSExpireDate4":  "Expiration date of the fourth TS per user CAL.",
    "msTAPI-IpAddress":  "IP addresses of a TAPI client computer that a user is logged into. This attribute can be used as a generic attribute to hold computer IP addresses.",
    "member":  "The list of users that belong to the group.",
    "uPNSuffixes":  "The list of User-Principal-Name suffixes for a domain.",
    "ipServiceProtocol":  "Contains the section of the services map that stores the protocol number for a UNIX service.",
    "objectClassCategory":  "This attribute contains the class type, such as abstract, auxiliary, or structured.",
    "msDS-MaximumPasswordAge":  "Maximum age for user account passwords.",
    "msDS-AllowedDNSSuffixes":  "The list of allowed suffixes for the dNSHostName attribute in computer objects.",
    "destinationIndicator":  "This is part of the X.500 specification and not used by NTDS.",
    "netbootSCPBL":  "A list of service connection points that reference this NetBoot server.",
    "msDS-ClaimSourceType":  "For a security principal claim type, lists the type of store the issued claim is sourced from",
    "options":  "A bitfield, where the meaning of the bits varies from objectClass to objectClass. Can occur on Inter-Site-Transport, NTDS-Connection, NTDS-DSA, NTDS-Site-Settings, and Site-Link objects.",
    "mS-SQL-Type":  "The replication type used by this SQL server. The following values are the possible values for this attribute.",
    "interSiteTopologyGenerator":  "This attribute will be used to support failover for the computer designated as the one that runs Knowledge Consistency Checker inter-site topology generation in a given site.",
    "dnsRecord":  "Used to store binary DNS resource records on DNS objects.",
    "machinePasswordChangeInterval":  "The Machine-Password-Change-Interval attribute is not currently used.",
    "msDS-Replication-Notify-First-DSA-Delay":  "This attribute controls the delay in time between changes to the DS, and notification of the first replica partner for an NC.",
    "ms-DS-UserPasswordNotRequired":  "Indicates whether a password is required for the account that this attribute references. True if a password is not required; otherwise, False.",
    "msSFU30NetgroupHostAtDomain":  "Contains part of the netgroup map that represents computed strings, such as \"host@domain\".",
    "prefixMap":  "The Prefix-Map attribute is for internal use only.",
    "internationalISDNNumber":  "Specifies an International ISDN Number associated with an object.",
    "msPKI-OIDLocalizedName":  "Contains a list of display names used to describe an OID by locale.",
    "nextRid":  "The Next Rid field used by the mixed mode allocator.",
    "mSMQComputerType":  "Replaced by MSMQ-Computer-Type-Ex.",
    "msKds-KDFAlgorithmID":  "The algorithm name of the key derivation function used to compute keys.",
    "mS-SQL-Applications":  "Not used. Default is set to, Appl 1, Appl 2, Appl 3, Appl 4, and Appl 5.",
    "msDS-AzBizRuleLanguage":  "The language that the business rule script is in (JScript, VBScript).",
    "msKds-UseStartTime":  "The time after which this root key may be used.",
    "mSMQSiteName":  "The MSMQ-Site-Name attribute has been replaced by the MSMQ-Site-Name-Ex attribute.",
    "msDFSR-OnDemandExclusionFileFilter":  "Filter string applied to on-demand replication files.",
    "msDS-Other-Settings":  "This multiple-valued attribute is used to store any configurable setting for the DS stored in the NAME=VALUE format.",
    "msTSSecondaryDesktopBL":  "This attribute represents the backward link to a user.",
    "audio":  "The Audio attribute type allows the storing of sounds in the Directory.",
    "msDFS-LinkSecurityDescriptorv2":  "Security descriptor of the DFS links\u0027s re-parse point on the file system.",
    "dNSHostName":  "Name of computer as registered in DNS.",
    "lastBackupRestorationTime":  "When the last system restore occurred.",
    "ipServicePort":  "Contains the section of the services map that specifies the port on which the UNIX service is available.",
    "mS-SQL-GPSLongitude":  "The MS-SQL-GPSLongitude attribute is not currently used.",
    "uSNLastObjRem":  "Contains the update sequence number (USN) for the last non-system object that was removed from a server.",
    "replPropertyMetaData":  "Tracks internal replication state information for DS objects. Information here can be extracted in public form through the public API DsReplicaGetInfo(). Present on all DS objects.",
    "queryPolicyBL":  "List of all objects holding references to a given Query-Policy.",
    "msDS-LastKnownRDN":  "Contains the original RDN of a deleted object.",
    "oncRpcNumber":  "Contains part of the RPC map and stores the RPC number for UNIX RPCs.",
    "gPCWQLFilter":  "Used to store a string that contains a GUID for the filter and a WMI namespace path.",
    "trustPartner":  "The name of the domain with which a trust exists.",
    "msTSProperty01":  "Reserved for future use.",
    "mSMQDependentClientService":  "Indicates whether this server provides dependent clients services.",
    "msCOM-UserPartitionSetLink":  "A link used to associate a User with a COM+ PartitionSet.",
    "contentIndexingAllowed":  "Indicates whether the Volume object can be content indexed.",
    "msSFU30MasterServerName":  "Contains the value that is returned when the NIS server processes a yp_master API call.",
    "msDS-AzOperationID":  "An application specific ID that makes the operation unique to the application.",
    "assetNumber":  "The tracking number for the object.",
    "lockOutObservationWindow":  "The range of time, in 100-nanosecond intervals, in which the system increments the incorrect logon count.",
    "dnsAllowDynamic":  "The Dns-Allow-Dynamic attribute is not currently used.",
    "dhcpMask":  "The dhcp-Mask attribute is not currently used.",
    "mS-SQL-Description":  "The description of the SQL Server database.",
    "msDS-HostServiceAccount":  "Service Accounts configured to run on this computer.",
    "associatedName":  "The associatedName attribute type specifies an entry in the organizational DIT that is associated with a DNS domain.",
    "givenName":  "Contains the given name (first name) of the user.",
    "groupMembershipSAM":  "Windows NT Security. Down level Windows NT support.",
    "aCSRSVPAccountFilesLocation":  "The directory location of RSVP account files. Defaults to the \"system32\" subdirectory located in the default system directory.",
    "msTAPI-ProtocolId":  "This attribute indicates the TAPI conference type. This attribute is used to determine how the binary BLOB in the ms-TAPI-Conference-Blob attribute is to be interpreted. This attribute is an arbitrary string, typically less than 100 characters in length.",
    "msDFSR-RootFence":  "Contains the root directory fence value.",
    "netbootGUID":  "Diskless boot: A computer\u0027s on-board GUID. Corresponds to the computer\u0027s network card MAC address.",
    "lastUpdateSequence":  "This attribute contains the update sequence number for the last item in the class store that was changed.",
    "msDS-ManagedPasswordInterval":  "This attribute is used to retrieve the number of days before a managed password is automatically changed for a group MSA.",
    "netbootNewMachineOU":  "Indicates where the new client computer account will be created.",
    "msDS-ManagedPasswordId":  "This attribute contains the key identifier for the current managed password data for a group MSA.",
    "msAuthz-ResourceCondition":  "For a central access rule, this attribute is an expression that identifies the scope of the target resource to which the policy applies.",
    "USNIntersite":  "The update sequence number (USN) for inter-site replication.",
    "trustType":  "The type of trust, for example, Windows NT or MIT.",
    "msSPP-PhoneLicense":  "License used during online activation of the Active Directory forest",
    "serverName":  "The name of a server.",
    "mS-SQL-Publisher":  "The name of the publisher database for replication.",
    "replTopologyStayOfExecution":  "The delay between deleting a server object and it being permanently removed from the replication topology.",
    "msDS-Behavior-Version":  "This attribute is used to track the domain or forest behavior version. It is a monotonically increasing number that is used to enable certain Active Directory features.",
    "msRADIUSFramedRoute":  "The msRADIUSFramedRoute attribute is used internally. Do not modify this value directly.",
    "mS-SQL-TCPIP":  "The TCP connection point.",
    "msDS-NC-RO-Replica-Locations":  "A linked attribute on a cross reference object for a partition. Lists the DC that should host the partition in a read-only manner.",
    "msPKI-RA-Application-Policies":  "The required RA application policy OID in the counter signatures of the certificate request.",
    "homePhone":  "The user\u0027s main home phone number.",
    "lSAModifiedCount":  "The LSA-Modified-Count attribute is used to support replication to Windows NT 4.0 domains.",
    "printMaxYExtent":  "The maximum vertical print region.",
    "preferredDeliveryMethod":  "The X.500-preferred way to deliver to addressee.",
    "msDS-NonMembers":  "This attribute serves the same purpose as the Non-Security-Member attribute but with scoping rules applied.",
    "trustParent":  "The parent in KERBEROS trust hierarchy.",
    "aCSDSBMPriority":  "This attributes contains the priority for this Subnet Bandwidth Manager (SBM). When a new Designated Subnet Bandwidth Manager (DSBM) needs to be elected, this value is sent to other SBMs in the domain as part of a DSBM_willing message. The SBM with the highest priority is elected as the new DSBM.",
    "msDS-RevealedDSAs":  "Backward link for ms-DS-Revealed-Users. Identifies which RODC holds that user\u0027s secret.",
    "logonCount":  "The number of times the account has successfully logged on. A value of 0 indicates that the value is unknown.",
    "currMachineId":  "The ID of the machine where a Link-Track-Vol-Entry object is located.",
    "fRSVersionGUID":  "If present, changing this value indicates a configuration change has been made on this replica set.",
    "msDS-Cached-Membership":  "The ms-DS-Cached-Membership attribute is used by the Security Accounts Manager for group expansion during token evaluation.",
    "shadowInactive":  "Contains the number of days of inactivity that is allowed for the user.",
    "msImaging-ThumbprintHash":  "Contains a hash of the security certificate for the Scan Repository/Secure Print Device.",
    "rpcNsGroup":  "A reference to an RPC Server entry or another RPC Group.",
    "createWizardExt":  "GUID of wizard extensions for creating associated object.",
    "msWMI-Int8Min":  "The minimum value for a WMI 64-bit integer parameter object.",
    "pKIEnrollmentAccess":  "The PKI-Enrollment-Access attribute is for internal use only.",
    "mSMQSites":  "The list of sites that the computer belongs to (an array of the sites\u0027 objectGUIDs, usually not more than one GUID).",
    "msDS-ClaimAttributeSource":  "For a claim type object, this attribute points to the attribute that will be used as the source for the claim type.",
    "x121Address":  "The X.121 address for an object.",
    "street":  "The street address.",
    "msTSLicenseVersion":  "Terminal server license version.",
    "printBinNames":  "A list of printer bin names.",
    "isRecycled":  "Is the object recycled. For use with AD Recycle Bin.",
    "msPKI-Enrollment-Servers":  "Priority, authentication type, and URI of each certificate enrollment web service.",
    "seqNotification":  "This attribute contains a counter that is incremented daily. This counter value is given to the link tracking service which adds the value to its volumes and link source files when they are refreshed. The domain controller maintains this value.",
    "lockoutDuration":  "The amount of time that an account is locked due to the Lockout-Threshold being exceeded. This value is stored as a large integer that represents the negative of the number of 100-nanosecond intervals from the time the Lockout-Threshold is exceeded that must elapse before the account is unlocked.",
    "msDS-RevealedUsers":  "Used with RODCs to identify the user objects whose secrets have been disclosed to that RODC.",
    "jpegPhoto":  "Used to store one or more images of a person using the JPEG File Interchange Format [JFIF].",
    "transportAddressAttribute":  "The name of the address type for the transport.",
    "objectClasses":  "A multiple-valued property that contains strings that represent each class in the schema. Each value contains the governsID, lDAPDisplayName, mustContain, mayContain, and so on.",
    "primaryInternationalISDNNumber":  "The primary ISDN.",
    "schemaFlagsEx":  "The Schema-Flags-Ex attribute is not currently used.",
    "previousCACertificates":  "Last expired certificate for this certification authority.",
    "revision":  "The revision level for a security descriptor or other change. Only used in the sam-server and ds-ui-settings objects.",
    "msKds-DomainID":  "Distinguished name of the Domain Controller which generated this root key.",
    "memberUid":  "Contains the login names of the members of a group.",
    "employeeNumber":  "The number assigned to an employee other than the ID.",
    "msCOM-PartitionLink":  "A link used to associate a COM+ PartitionSet with its constituent COM+ Partitions.",
    "globalAddressList2":  "This attribute is used on a Microsoft Exchange container to store the distinguished name of a newly created global address list (GAL)",
    "defaultHidingValue":  "A Boolean value that specifies the default setting of the showInAdvancedViewOnly property of new instances of this class.",
    "msDFSR-StagingCleanupTriggerInPercent":  "Staging clean-up trigger in percent of free disk space.",
    "aCSNonReservedTxLimit":  "The maximum bandwidth a user application can transmit before a reservation is in place.",
    "department":  "Contains the name for the department in which the user works.",
    "netbootAnswerOnlyValidClients":  "Determines whether the server answers all, or only pre-staged, client computers.",
    "printMaxXExtent":  "The maximum horizontal print region.",
    "msDS-ManagedPassword":  "This constructed attribute returns a blob that contains the clear-text previous and current password, TimeUntilEpochExpires, and TimeUntilNextScheduledUpdate for a group MSA.",
    "groupType":  "Contains a set of flags that define the type and scope of a group object. For the possible values for this attribute, see Remarks.",
    "msDS-Entry-Time-To-Die":  "Holds the absolute expiration time of a dynamic object in the directory.",
    "msDS-AzMajorVersion":  "Major version number for AzRoles.",
    "uSNChanged":  "The update sequence number (USN) assigned by the local directory for the latest change, including creation. See also , USN-Created.",
    "ms-DS-UserEncryptedTextPasswordAllowed":  "Indicates whether Active Directory will store the password in the reversible encryption format. True if the password is stored in the reversible encryption format; otherwise, False.",
    "msSFU30FieldSeparator":  "Contains the field separator for each NIS map.",
    "rIDAllocationPool":  "A pool that was prefetched for use by the RID manager when the RID-Previous-Allocation-Pool has been used up.",
    "bootFile":  "Contains the name of the boot image for UNIX identity management support.",
    "otherHomePhone":  "A list of alternate home phone numbers.",
    "msDS-AzDomainTimeout":  "Time, in milliseconds, after a domain is detected to be unreachable and before the DC is tried again.",
    "lastKnownParent":  "The Distinguished Name (DN) of the last known parent of an orphaned object.",
    "msDS-IsUserCachableAtRodc":  "For a Read-Only Domain Controller (RODC), identifies whether the specified user\u0027s secrets can be cached.",
    "ipsecOwnersReference":  "The Ipsec-Owners-Reference attribute is for internal use only.",
    "postOfficeBox":  "The post office box number for this object.",
    "msPKI-OID-Attribute":  "The attribute for the enterprise OID.",
    "ipHostNumber":  "Contains the IP address of the host in dotted decimal notation, omitting the leading zeros.",
    "fRSPartnerAuthLevel":  "The RPC Security level.",
    "mS-SQL-NamedPipe":  "The named pipe connection.",
    "schemaIDGUID":  "The unique identifier for an attribute.",
    "printNetworkAddress":  "The user-supplied network address.",
    "ipsecID":  "The Ipsec-ID attribute is for internal use only.",
    "registeredAddress":  "Specifies a mnemonic for an address associated with an object at a particular city location. The mnemonic is registered in the country/region in which the city is located and is used in the provision of the Public Telegram Service.",
    "bridgeheadServerListBL":  "The list of servers that are bridgeheads for replication.",
    "msDS-UserDontExpirePassword":  "Indicates whether the password for the account this attribute references will expire. True if the password will not expire; otherwise, False.",
    "ipNetmaskNumber":  "Contains the IP netmask in dotted decimal notation, omitting the leading zeros.",
    "privilegeDisplayName":  "Display name for a Windows\u0026nbsp;NT privilege.",
    "volumeCount":  "The tracked volume quota for a given computer.",
    "msDS-AzBizRule":  "Text of the script implementing the business rule.",
    "msWMI-Parm1":  "The ms-WMI-Parm1 attribute is reserved for internal use.",
    "msDS-IsPartialReplicaFor":  "Backward link for hasPartialReplicaNCs. Identifies which DCs hold that partition as a partial replica.",
    "msDNS-DNSKEYRecordSetTTL":  "An attribute that defines the time-to-live (TTL) value assigned to DNSKEY records when signing the DNS zone.",
    "cAConnect":  "The connection string for binding to a certification authority.",
    "foreignIdentifier":  "The security properties used by a foreign system.",
    "privilegeValue":  "A value that represents an Windows\u0026nbsp;NT privilege.",
    "rpcNsTransferSyntax":  "The UUID of the transfer syntax supported by the current entry.",
    "minPwdLength":  "The minimum number of characters that a password must contain.",
    "lDAPAdminLimits":  "Contains a set of attribute-value pairs defining LDAP server administrative limits.",
    "postalCode":  "The postal or zip code for mail delivery.",
    "serviceClassName":  "The string name of the service that an administration point represents.",
    "associatedDomain":  "The associatedDomain attribute type specifies a DNS domain that is associated with an object.",
    "employeeID":  "The ID of an employee.",
    "mSMQInRoutingServers":  "DN links to MSMQ routing servers through which all incoming traffic to this computer should be routed.",
    "meetingURL":  "The web address for the meeting.",
    "publicKeyPolicy":  "Reference to the Public Key policy for this domain.",
    "portName":  "List of port names. For example, for printer ports or comm ports.",
    "systemMayContain":  "The list of optional attributes for a class. The list of attributes can only be modified by the system.",
    "fRSWorkingPath":  "The path to the file replication database.",
    "cRLObject":  "Reference to certificate revocation list object associated with a certification authority.",
    "distinguishedName":  "Same as the Distinguished Name for an object. Used by Exchange.",
    "printSeparatorFile":  "The file path of the printer separator page.",
    "msDFSR-Enabled":  "Specifies whether the object is enabled.",
    "rightsGuid":  "The GUID used to represent an extended right within an access control entry.",
    "msDFS-LinkPathv2":  "DFS link path relative to the DFS root target share (that is, without the server/domain and DFS namespace name components). Use forward slashes (/) instead of backslashes (\\), so that LDAP searches can be done without having to use escapes.",
    "rid":  "The relative Identifier of an object.",
    "dhcpSites":  "The dhcp-Sites attribute is not currently used.",
    "attributeCertificateAttribute":  "A digitally signed or certified identity and set of attributes. Used to bind authorization information to an identity.",
    "dSUIShellMaximum":  "This is the default maximum number of objects to be shown in a container by the shell UI.",
    "msDFSR-Extension":  "Contains the Distributed File System (DFS) Replication extension attribute.",
    "mS-SQL-SortOrder":  "The sort order for the current instance of SQL Server.",
    "pekKeyChangeInterval":  "Password encryption key change interval.",
    "partialAttributeDeletionList":  "Tracks the internal replication state of partial replicas (that is, on GCs). Attribute of the partial replica NC object. Used when the GC is in the process of removing attributes from the objects in its partial replica NCs.",
    "aCSMaximumSDUSize":  "The ACS-Maximum-SDU-Size attribute is for internal use only. Based on RFC2210.",
    "msDS-ServiceAccountBL":  "Backward link reference to the AD/AM DSA object that uses this service account.",
    "msDS-KrbTgtLink":  "Used with RODCs to define which krbtgt_XXXX account corresponds to each RODC.",
    "msDS-PerUserTrustTombstonesQuota":  "Used to enforce a per-user quota for deleting Trusted-Domain objects when authorization is based on matching the user\u0027s SID.",
    "msDFSR-Options":  "Contains the Distributed File System (DFS) Replication object options.",
    "aCSDSBMRefresh":  "This attribute contains the interval timer value that determines when the Designated Subnet Bandwidth Manager (DSBM) sends out a refresh message (I_AM_DSBM) to all of the Subnet Bandwidth Managers in a domain.",
    "schedule":  "A schedule BLOB as defined by the Windows NT Job Service. Used by replication.",
    "msDFSR-Options2":  "Object Options2.",
    "msTSManagingLS":  "Terminal Services managing license server.",
    "cOMUniqueLIBID":  "Single-valued string GUID LIBID for a type library.",
    "msRASSavedFramedIPAddress":  "The msRASSavedFramedIPAddress attribute is used internally. Do not modify this value directly.",
    "isPrivilegeHolder":  "Backward link to privileges held by a given principal.",
    "attributeSecurityGUID":  "The GUID to be used to apply security credentials to a set of objects.",
    "netbootSIFFile":  "The Netboot-SIF-File attribute is reserved for internal use.",
    "unixHomeDirectory":  "Contains the absolute path to the home directory.",
    "netbootMachineFilePath":  "This attribute specifies the server that answers the client. Beginning with the Windows Server 2003 operating system, it can indicate the Startrom.com that the client gets.",
    "machineArchitecture":  "Contains a list of hardware processors supported by a given application.",
    "msiFileList":  "This attribute contains a list of Microsoft installer files, such as the base MSI file (.msi) and MST transform files (.mst).",
    "pwdLastSet":  "The date and time that the password for this account was last changed. This value is stored as a large integer that represents the number of 100 nanosecond intervals since January 1, 1601 (UTC). If this value is set to 0 and the User-Account-Control attribute does not contain the UF_DONT_EXPIRE_PASSWD flag, then the user must set the password at the next logon.",
    "msDFSR-DisablePacketPrivacy":  "Disable packet privacy on a connection.",
    "msiScript":  "The Microsoft installer advertisement script for this application.",
    "msDS-AllowedToActOnBehalfOfOtherIdentity":  "This attribute is used for access checks to determine if a requestor has permission to act on the behalf of other identities to services running as this account.",
    "msDS-MembersOfResourcePropertyList":  "For a resource property list object, this multi-valued link attribute points to one or more resource property objects.",
    "msSPP-ConfigLicense":  "Product-key configuration license used during online/phone activation of the Active Directory forest.",
    "helpData16":  "This attribute was used for the Win16 help file format for Exchange 4.0. It is not used for any other versions of Exchange.",
    "cACertificate":  "Certificates of trusted Certification Authorities.",
    "rangeUpper":  "The maximum value or length of an attribute.",
    "qualityOfService":  "Local or domain quality of service bits on policy objects.",
    "msDS-GeoCoordinatesLatitude":  "Geo-coordinates for location services, specifically, latitude (in microdegrees) of the office or conference room.",
    "mSMQComputerTypeEx":  "A string that describes the operating system version and the MSMQ version.",
    "remoteServerName":  "Used wherever one or more computer names must be stored.",
    "msDS-TombstoneQuotaFactor":  "The percentage factor by which the tombstone object count should be reduced for the purpose of quota accounting.",
    "msPKIAccountCredentials":  "Storage of encrypted user credential token BLOBs for roaming.",
    "msDS-AzClassId":  "A class ID required by the AzRoles UI on the AzApplication object.",
    "fSMORoleOwner":  "Flexible Single-Master Operation: The distinguished name of the DC where the schema can be modified.",
    "tokenGroupsGlobalAndUniversal":  "Token groups for Exchange.",
    "msKds-CreateTime":  "The time when this root key was created.",
    "templateRoots2":  "This attribute is used on the Exchange config container to indicate where the template containers are stored. This information is used by the Active Directory MAPI provider",
    "hasPartialReplicaNCs":  "Sibling to Has-Master-NCs. Has-Partial-Replica-NCs reflects the distinguished name for all other-domain NCs that have been replicated into a global catalog.",
    "msDS-Approx-Immed-Subordinates":  "The value returned by this attribute is based on index sizes. This may be off by +/ 10% on large containers, and the error is theoretically unbounded, but using this attribute helps the UI display the contents of a container.",
    "meetingLanguage":  "The language for a meeting.",
    "meetingDescription":  "The description of the meeting.",
    "msWMI-TargetClass":  "Class name of the policy object to be created.",
    "serviceBindingInformation":  "Service-specific binding information in string format.",
    "msRADIUS-FramedIpv6Route":  "Provides routing information to be configured for the user on the NAS.",
    "msDS-Settings":  "Used to store settings for an object. Its use is solely determined by the object\u0027s owner. We recommend using it to store name/value pairs. For example, color=blue.",
    "keywords":  "A list of keywords that can be used to locate a given connection point.",
    "msDS-ClaimIsValueSpaceRestricted":  "For a claim type, this attribute identifies whether a user can input values other than those described in the msDS-ClaimPossibleValues in applications.",
    "assistant":  "The distinguished name of a user\u0027s administrative assistant.",
    "rpcNsEntryFlags":  "Flag to indicate that the RPC NS entry was explicitly created.",
    "forceLogoff":  "Used in computing the kick off time in SamIGetAccountRestrictions. Logoff time minus Force Log off equals kick off time.",
    "perRecipDialogDisplayTable":  "The Per Recipient options MAPI display table.",
    "netbootLimitClients":  "The netboot-Limit-Clients attribute is reserved for internal use.",
    "msDFSR-Schedule":  "Contains the replication schedule for the Distributed File System (DFS) Replication service.",
    "dhcpClasses":  "The dhcp-Classes attribute is not currently used.",
    "securityIdentifier":  "A unique value of variable length used to identify a user account, group account, or logon session to which an ACE applies.",
    "msWMI-TargetNameSpace":  "Namespace in which the object is to be created.",
    "aCSMaxAggregatePeakRatePerUser":  "The maximum peak rate any user may have for all flows.",
    "msDS-NCReplInboundNeighbors":  "Replication partners for this partition. This server obtains replication data from these other servers, which act as sources.",
    "msDS-PasswordReversibleEncryptionEnabled":  "Password-reversible encryption status for user accounts.",
    "msWMI-intFlags1":  "The ms-WMI-intFlags1 attribute is reserved for internal use.",
    "mSMQMigrated":  "The MSMQ-Migrated attribute contains MSMQ mixed-mode information.",
    "dhcpIdentification":  "The dhcp-Identification attribute is not currently used.",
    "msDs-Schema-Extensions":  "A binary BLOB used to store information about extensions to schema objects.",
    "adminDisplayName":  "The name to be displayed on admin screens.",
    "frsComputerReference":  "Reference to a replica set member\u0027s computer object.",
    "msDS-IsFullReplicaFor":  "Backward link for ms-DS-Has-Domain-NCs. Identifies which DCs hold that partition as their primary domain.",
    "msRRASAttribute":  "String that contains the vendorID:Number installed on a router. The VendorID is vendor SMI and the Number is ms-RRAS-Attribute Number defined in the Router Identity Dictionary Object.",
    "meetingKeyword":  "A keyword that is used to help find a meeting.",
    "extendedCharsAllowed":  "Indicates whether extended characters are allowed in the value of this attribute. Only applies to IA5, Numeric, Printable, and Teletex string attributes.",
    "msDFSR-OnDemandExclusionDirectoryFilter":  "Filter string applied to on-demand replication directories.",
    "msDS-PhoneticDepartment":  "Contains the phonetic department name where the person works.",
    "fRSPrimaryMember":  "Reference to the primary member of a replica set.",
    "labeledURI":  "A Uniform Resource Identifier followed by a label. The label is used to describe the resource to which the URI points, and is intended as a display name that is human-readable.",
    "mail":  "The list of email addresses for a contact.",
    "msDS-UserAccountDisabled":  "Indicates whether an account is disabled or enabled. True if the account disabled; otherwise, False.",
    "fRSFlags":  "The FRS option flags.",
    "msDS-Site-Affinity":  "The ms-DS-Site-Affinity attribute is used by the Security Accounts Manager for group expansion during token evaluation.",
    "msDS-RevealedList":  "Identifies security principals whose current computer account passwords have been replicated to the RODC.",
    "createTimeStamp":  "The date when this object was created. This value is replicated.",
    "rIDUsedPool":  "The RID Pools that have been used by a DC.",
    "gecos":  "Contains the information that is stored in the GECOS field.",
    "badPasswordTime":  "The last time and date that an attempt to log on to this account was made with a password that is not valid. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of zero means that the last time an incorrect password was used is unknown.",
    "msDS-PhoneticCompanyName":  "Contains the phonetic company name where the person works.",
    "msDS-AzScopeName":  "A string that uniquely identifies a scope object.",
    "msSFU30NisDomain":  "Contains the NIS domain.",
    "fRSServiceCommandStatus":  "The response from the last command issued to a member.",
    "msDS-GeoCoordinatesAltitude":  "Geo-coordinates for location services, specifically altitude (in millimeters) of the office or conference room.",
    "currentValue":  "LSA secret - Current secret value.",
    "telephoneNumber":  "The primary telephone number.",
    "netbootAnswerRequests":  "Enables the RIS server to accept any RIS requests.",
    "ipPhone":  "The TCP/IP address for the phone. Used by Telephony.",
    "msDNS-SigningKeyDescriptors":  "An attribute that contains the set of DNSSEC Signing Key Descriptors (SKDs) used by the DNS server to generate keys and sign the DNS zone.",
    "productCode":  "This attribute contains a unique identifier for an application for a particular product release, represented as a string GUID, for example \"{12345678-1234-1234-1234-123456789012}\". Letters used in this GUID must be uppercase. This ID must vary for different versions and languages.",
    "msWMI-Author":  "The author of an instance of a class.",
    "dhcpSubnets":  "The dhcp-Subnets attribute is not currently used.",
    "treeName":  "DNS name of the domain at the root of a tree.",
    "bytesPerMinute":  "Printer data transfer rate.",
    "msDS-ExternalStore":  "A string that identifies the location of an external store, such as a database.",
    "meetingOwner":  "The person responsible for the meeting.",
    "msTSReconnectionAction":  "Terminal Services session Reconnection Action specifies whether to allow reconnection to a disconnected Terminal Services session from any client computer.",
    "lockoutTime":  "The date and time (UTC) that this account was locked out. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of zero means that the account is not currently locked out.",
    "msDS-ClaimSharesPossibleValuesWith":  "For a resource property object, this attribute indicates that the suggested values of the claims issued are defined on the object that this linked attribute points to. Overrides ms-DS-Claim-Possible-Values on itself, if populated.",
    "superScopeDescription":  "This attribute provides a description for a superscope.",
    "msDS-DeletedObjectLifetime":  "Lifetime of a deleted object.",
    "birthLocation":  "The location where the user was born.",
    "msDS-AuthenticatedAtDC":  "Forward link for ms-DS-AuthenticatedTo-Accountlist. Identifies which DC a user has authenticated to.",
    "msDS-QuotaTrustee":  "The SID of the security principal for which the quota is being assigned.",
    "mSMQQueueType":  "A GUID that describes the type of service provided by the queue.",
    "addressBookRoots":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "parentCA":  "The distinguished name of a certification authority (CA) object for a parent CA.",
    "msPKI-OID-CPS":  "The CPS (Certificate Policy Statement) for the enterprise issuer policy OID.",
    "netbootInitialization":  "Default boot path for diskless boot.",
    "serviceDNSName":  "The DNS name to look up to find a server running this service.",
    "serviceInstanceVersion":  "Version of a Winsock service.",
    "msDS-EnabledFeatureBL":  "Scopes where this optional feature is enabled.",
    "msDNS-NSEC3Iterations":  "An attribute that defines how many NSEC3 hash iterations to perform when signing the DNS zone.",
    "knowledgeInformation":  "Specifies a human-readable accumulated description of knowledge mastered by a specific Directory System Agent.",
    "msWMI-ChangeDate":  "The last date this object was changed.",
    "dBCSPwd":  "The account\u0027s LAN Manager password.",
    "drink":  "The drink (Favorite Drink) attribute type specifies the favorite drink of an object (or person).",
    "mSMQSiteNameEx":  "The MSMQ-Site-Name-Ex attribute contains MSMQ mixed-mode information.",
    "msWMI-intFlags3":  "The ms-WMI-intFlags3 attribute is reserved for internal use.",
    "mS-SQL-SPX":  "The SPX connection point.",
    "documentPublisher":  "The documentPublisher attribute is the person or organization that published a document.",
    "mS-DS-ConsistencyGuid":  "This attribute is used to check consistency between the directory and another object, database, or application, by comparing GUIDs.",
    "dnsRoot":  "The uppermost DNS domain name assigned to a domain/directory partition. This is set on a crossRef object and is used, among other things, for referral generation. When searching through an entire domain tree, the search must be initiated at the Dns-Root object. This attribute can be multi-valued, in which case multiple referrals are generated.",
    "meetingScope":  "Can be global, local, or some other value. Its interpretation is application specific.",
    "msDFSR-Flags":  "Contains the Distributed File System (DFS) Replication service object flags.",
    "msDNS-SigningKeys":  "An attribute that contains the set of encrypted DNSSEC signing keys used by the DNS server to sign the DNS zone.",
    "printMediaSupported":  "A list of media supported by a printer.",
    "meetingLocation":  "The location for a meeting.",
    "msWMI-Name":  "The display name for top-level policy objects. Used in the Global Catalog.",
    "ou":  "The name of the organizational unit.",
    "generatedConnection":  "True if this connection was created by auto topology generation.",
    "dhcpState":  "The dhcp-State attribute is not currently used.",
    "invocationId":  "Used to uniquely identify each Microsoft Exchange Server directory in the organization.",
    "mailAddress":  "Generic mail address attribute. Used in the box as an optional attribute of server objects, where it is consumed by mail-based DS replication (if the computers are so configured).",
    "documentAuthor":  "The documentAuthor attribute type specifies the distinguished name of the author of a document.",
    "certificateRevocationList":  "Represents a list of certificates that have been revoked.",
    "hasMasterNCs":  "The distinguished name for the naming contexts for the DC. Forward link for the Mastered-By attribute.",
    "enrollmentProviders":  "PKI - Certificate Templates.",
    "primaryTelexNumber":  "The primary telex number.",
    "cOMInterfaceID":  "This attribute stores the list of interfaces that are implemented in this application package.",
    "ms-net-ieee-8023-GP-PolicyReserved":  "Reserved for future use.",
    "dhcpRanges":  "The dhcp-Ranges attribute is not currently used.",
    "meetingApplication":  "This attribute contains the name of the application that will be used for an online meeting, such as Microsoft NetMeeting, Windows Media Services, or Microsoft Exchange Conferencing.",
    "msDS-AzObjectGuid":  "The unique and portable identifier of AzMan objects",
    "msIIS-FTPRoot":  "This attribute determines the file server share. It is used in conjunction with ms-IIS-FTP-Dir to determine the FTP user home directory.",
    "serviceClassInfo":  "General Service Class information.",
    "systemAuxiliaryClass":  "A list of auxiliary classes that cannot be modified by the user.",
    "msDRM-IdentityCertificate":  "The XrML digital rights management certificates for this user.",
    "physicalDeliveryOfficeName":  "Contains the office location in the user\u0027s place of business.",
    "printKeepPrintedJobs":  "TRUE if printed jobs are kept.",
    "interSiteTopologyFailover":  "Indicates how much time must transpire since the last keep-alive for the inter-site topology generator to be considered dead.",
    "dnsNotifySecondaries":  "The Dns-Notify-Secondaries attribute is not currently used.",
    "aCSNonReservedTxSize":  "The token bucket size an application can use before a reservation is in place.",
    "allowedAttributesEffective":  "A list of attributes that can be modified on the object.",
    "sAMAccountName":  "The logon name used to support clients and servers running earlier versions of the operating system, such as Windows NT 4.0, Windows 95, Windows 98, and LAN Manager.",
    "siteServer":  "Licensing main server for a given Site.",
    "meetingIP":  "The TCP/IP address for the meeting.",
    "nETBIOSName":  "The name of the object to be used over NetBIOS.",
    "msDS-ReplicationEpoch":  "This is used to hold the epoch under which all the DCs are replicating. An epoch is the period of time in which a domain has a specific name. A new epoch starts when a domain name change occurs.",
    "aNR":  "Ambiguous name resolution attribute to be used when choosing between objects.",
    "msWMI-ScopeGuid":  "The GUID for the scope in which the associated encoding is located.",
    "nTSecurityDescriptor":  "The Windows NT security descriptor for the schema object. A security descriptor is a data structure that contains security information about an object, such as the ownership and permissions of the object.",
    "msDFSR-ComputerReferenceBL":  "Contains the backward link for the ms-DFSR-ComputerReference attribute.",
    "fRSRootSecurity":  "The security descriptor of replica set root for file replication.",
    "modifiedCount":  "Net Logon Change Log serial number.",
    "msDS-MinimumPasswordAge":  "Minimum age for user account passwords.",
    "transportType":  "The distinguished name for a type of transport being used to connect sites together. This value can point to an IP or SMTP transport.",
    "subSchemaSubEntry":  "The distinguished name for the location of the subschema object where a class or attribute is defined.",
    "msTSExpireDate":  "TS expiration date.",
    "mS-SQL-InformationDirectory":  "The informational directory for the current instance of SQL Server.",
    "groupsToIgnore":  "The Groups-to-Ignore attribute is not currently used.",
    "pKIDefaultKeySpec":  "The private key specification for the certificate template.",
    "lockoutThreshold":  "The number of invalid logon attempts that are permitted before the account is locked out.",
    "msDS-RevealOnDemandGroup":  "Used with RODCs to define which users, computers, and groups are allowed to have their passwords cached on an RODC.",
    "mSMQLabelEx":  "A string that describes the type of service provided by the queue.",
    "msAuthz-CentralAccessPolicyID":  "For a Central Access Policy, this attribute defines a GUID that can be used to identify the set of policies when applied to a resource.",
    "fRSMemberReferenceBL":  "Reference to subscriber objects for this member.",
    "dhcpMaxKey":  "The dhcp-MaxKey attribute is not currently used.",
    "operatingSystemHotfix":  "The hotfix level of the operating system.",
    "msDNS-MaintainTrustAnchor":  "An attribute used to define the type of trust anchor to automatically publish in the forest-wide trust anchor store when the DNS zone is signed.",
    "objectGUID":  "The unique identifier for an object.",
    "msDNS-KeymasterZones":  "A list of Active Directory-integrated zones for which the DNS server is the keymaster.",
    "certificateAuthorityObject":  "Reference to the certification authority associated with a Certificate Revocation List distribution point.",
    "thumbnailPhoto":  "An image of the user. A space-efficient format like JPEG or GIF is recommended.",
    "nextLevelStore":  "This attribute indicates the next class store to search.",
    "msFRS-Hub-Member":  "The ms-FRS-Hub-Member attribute is used to record the preferred NTFRS topology settings. When an FRS member gets added or deleted to a replica set, these attributes are referred and appropriate adjustments are made to the connections between the rest of the FRS members in the replica set.",
    "msTSLicenseVersion2":  "Version of the second terminal server per user CAL.",
    "mS-SQL-GPSLatitude":  "The MS-SQL-GPSLatitude attribute is not currently used.",
    "additionalTrustedServiceNames":  "A list of services in the domain that can be trusted. Not used by AD.",
    "samDomainUpdates":  "Contains a bitmask of performed SAM operations on Active Directory.",
    "msDFSR-Priority":  "Priority level.",
    "nTGroupMembers":  "This attribute is not used.",
    "msNPAllowDialin":  "Indicates whether the account has permission to dial in to the RAS server. Do not modify this value directly. Use the appropriate RAS administration function to modify this value.",
    "categoryId":  "The ID for a component category.",
    "installUiLevel":  "This attribute contains information for the type (level) of installation that is used for the user interface. Possible installation levels are as follows: 2 INSTALLUILEVEL_NONE (silent installation) 3 INSTALLUILEVEL_BASIC (simple installation with error handling) 4 INSTALLUILEVEL_REDUCED (authored UI, wizard dialog boxes suppressed) 5 INSTALLUILEVEL_FULL (authored UI with wizards, progress, errors)",
    "driverName":  "The device driver name.",
    "msSFU30OrderNumber":  "Contains a value that is used by NIS to check if the map has changed. Every time the data stored in the msSFU-30-Domain-Info object changes, this value is incremented. This value is used to track data changes between ypxfer calls.",
    "optionsLocation":  "For DHCP, the options location contains the DN for alternate sites that contain the options information.",
    "dNSTombstoned":  "True if this object has been tombstoned. This attribute exists to make searching for tombstoned records easier and faster. Tombstoned objects are objects that have been deleted but not yet removed from the directory.",
    "aCSNonReservedPeakRate":  "The ACS-Non-Reserved-Peak-Rate attribute is for internal use only. Based on RFC2814.",
    "extensionName":  "The name of a property page used to extend the UI of a directory object.",
    "nTMixedDomain":  "Indicates that the domain is in native mode or mixed mode. This attribute is found in the domainDNS (head) object for the domain.",
    "nisNetgroupTriple":  "Represents one entry from a netgroup map.",
    "lDAPIPDenyList":  "Holds a list of binary IP addresses that are denied access to an LDAP server.",
    "linkTrackSecret":  "This attribute stores a link to a secret key that allows an encrypted file to be translated to plaintext.",
    "mS-SQL-Memory":  "The amount of memory on the computer.",
    "dhcpOptions":  "The dhcp-Options attribute is not currently used.",
    "personalTitle":  "The user\u0027s title.",
    "ipsecISAKMPReference":  "The Ipsec-ISAKMP-Reference attribute is for internal use only.",
    "msDS-IsUsedAsResourceSecurityAttribute":  "For a resource property, this attribute indicates whether it is being used as a secure attribute.",
    "msDS-PhoneticFirstName":  "Contains the phonetic given name or first name of the person.",
    "systemOnly":  "A Boolean value that specifies whether only Active Directory can modify the class. System-only classes can only be created or deleted by the Directory System Agent.",
    "searchGuide":  "Specifies information of suggested search criteria, which may be included in some entries that are expected to be a convenient base-object for the search operation, for example, country/region or organization.",
    "unstructuredAddress":  "The IP address of the router (for example: ************).",
    "aCSMinimumDelayVariation":  "The ACS-Minimum-Delay-Variation attribute is for internal use only. Based on RFC2210.",
    "msDS-ResultantPSO":  "Resultant password settings object applied to this object.",
    "supplementalCredentials":  "Stored credentials for use in authenticating. The encrypted version of the user\u0027s password. This attribute is neither readable nor writable.",
    "userAccountControl":  "Flags that control the behavior of the user account.",
    "msDFSR-CachePolicy":  "On-demand cache policy options.",
    "l":  "Represents the name of a locality, such as a town or city.",
    "siteObject":  "The distinguished name for the site to which this subnet belongs.",
    "msPKI-Site-Name":  "Active Directory site to which the CA computer belongs.",
    "aCSPriority":  "Relative priority of a flow for this user.",
    "msDFS-LastModifiedv2":  "To be updated on each write to the entry that contains the attribute.",
    "garbageCollPeriod":  "This attribute is located on the CN=Directory Service,CN=Windows NT,CN=Services,CN=Configuration,... object. It represents the time, in hours, between DS garbage collection runs.",
    "pendingParentCA":  "Reference to the certification authorities that issued the pending certificates for this certification authority.",
    "canonicalName":  "The name of the object in canonical format. myserver2.fabrikam.com/users/jeffsmith is an example of a distinguished name in canonical format. This is a constructed attribute. The results returned are identical to those returned by the following Active Directory function: DsCrackNames(NULL, DS_NAME_FLAG_SYNTACTICAL_ONLY, DS_FQDN_1779_NAME, DS_CANONICAL_NAME, ...).",
    "msWMI-intFlags2":  "The ms-WMI-intFlags2 attribute is reserved for internal use.",
    "crossCertificatePair":  "V3 Cross Certificate.",
    "aCSTotalNoOfFlows":  "The total number of flows that a given user may have.",
    "ownerBL":  "The backward link to the owner attribute. Contains a list of owners for an object.",
    "shadowFlag":  "Contains the section of the shadow map that is used to store the flag value.",
    "msDS-ExternalKey":  "A string that identifies an object in an external store, such as a record in a database.",
    "interSiteTopologyRenew":  "This class indicates how often the intersite topology generator updates the keep-alive message that is sent to domain controllers contained in the same site.",
    "createDialog":  "GUID of dialog box for creating associated object.",
    "profilePath":  "Specifies a path to the user\u0027s profile. This value can be a null string, a local absolute path, or a UNC path.",
    "appliesTo":  "Contains the list of object classes that the extended right applies to. In the list, an object class is represented by the schemaIDGUID property for its schemaClass object.",
    "lastLogonTimestamp":  "This is the time that the user last logged into the domain. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). Whenever a user logs on, the value of this attribute is read from the DC. If the value is older [ current_time - msDS-LogonTimeSyncInterval ], the value is updated. The initial update after the raise of the domain functional level is calculated as 14 days minus random percentage of 5 days.",
    "mSMQCSPName":  "The type of cryptographic provider used by MSMQ.",
    "ms-net-ieee-8023-GP-PolicyData":  "Contains all of the settings and data that make up a Group Policy configuration for 802.3 wired networks.",
    "gPCFileSysPath":  "True if the object is enabled.",
    "msDS-SecondaryKrbTgtNumber":  "Identifies the protocol identification number associated with the secondary domain.",
    "mS-SQL-RegisteredOwner":  "The registered owner for the current instance of SQL Server.",
    "dc":  "The naming attribute for Domain and DNS objects. Usually displayed as dc=DomainName.",
    "serverState":  "Indicates whether the server is enabled or disabled. A value of 1 indicates that the server is enabled. A value of 2 indicates that the server is disabled. All other values are invalid.",
    "msDS-ValueTypeReference":  "This attribute is used to link a resource property object to its value type.",
    "printNotify":  "A user-supplied string that specifies the notification contact.",
    "organizationalStatus":  "The organizationalStatus attribute type specifies a category by which a person is often referred to in an organization.",
    "streetAddress":  "The user\u0027s address."
}
