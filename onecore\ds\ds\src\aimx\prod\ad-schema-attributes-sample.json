﻿{
    "aCSMinimumLatency":  "The ACS-Minimum-Latency attribute is for internal use only. Based on RFC2210.",
    "aCSMaxAggregatePeakRatePerUser":  "The maximum peak rate any user may have for all flows.",
    "aCSNonReservedTokenSize":  "The ACS-Non-Reserved-Token-Size attribute is for internal use only. Based on RFC2814.",
    "aCSNonReservedMaxSDUSize":  "The ACS-Non-Reserved-Max-SDU-Size attribute is for internal use only. Based on RFC2814.",
    "aCSDirection":  "Send, Receive, Send/Receive, or None.",
    "aCSPriority":  "Relative priority of a flow for this user.",
    "aCSCacheTimeout":  "The amount of time before the expiration of ACS objects that are cached by the ACS service.",
    "aCSMaxPeakBandwidthPerFlow":  "The peak bandwidth any flow can consume.",
    "aCSNonReservedTxLimit":  "The maximum bandwidth a user application can transmit before a reservation is in place.",
    "aCSEnableACSService":  "True if ACS service is to be enabled.",
    "aCSEnableRSVPMessageLogging":  "True if RSVP logging is enabled.",
    "aCSMaxDurationPerFlow":  "The maximum duration, in seconds, of any single flow.",
    "aCSAggregateTokenRatePerUser":  "The maximum token rate any user may have for all flows.",
    "aCSMinimumPolicedSize":  "The ACS-Minimum-Policed-Size attribute is for internal use only. Based on RFC2210.",
    "aCSTotalNoOfFlows":  "The total number of flows that a given user may have.",
    "aCSMaximumSDUSize":  "The ACS-Maximum-SDU-Size attribute is for internal use only. Based on RFC2210.",
    "aCSMaxTokenBucketPerFlow":  "The ACS-Max-Token-Bucket-Per-Flow attribute is for internal use only. Based on RFC2210.",
    "aCSRSVPAccountFilesLocation":  "The directory location of RSVP account files. Defaults to the \"system32\" subdirectory located in the default system directory.",
    "aCSMaxSizeOfRSVPLogFile":  "The maximum size, in bytes, of an RSVP log file.",
    "accountExpires":  "The date when the account expires. This value represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of 0 or 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicates that the account never expires. Accounts configured to never expire may have either value, depending on whether they were originally configured with an expiration value, with 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicating that the account has not been previously configured to expire.",
    "aCSIdentityName":  "This attribute contains the DN of a user or OU and is the identity of a user or a group of users to which this QoS policy applies.",
    "aCSMaxTokenRatePerFlow":  "The maximum token rate any single flow may have for a given user.",
    "additionalTrustedServiceNames":  "A list of services in the domain that can be trusted. Not used by AD.",
    "accountNameHistory":  "The length of time that the account has been active.",
    "aCSServerList":  "Contains the DNS names of Windows NT servers that are allowed to run ACS.",
    "aCSMaxNoOfLogFiles":  "The maximum number of RSVP log files.",
    "aCSEnableRSVPAccounting":  "True if RSVP accounting is enabled.",
    "aCSEventLogLevel":  "Was this page helpful?",
    "aCSNonReservedTxSize":  "The token bucket size an application can use before a reservation is in place.",
    "aCSTimeOfDay":  "Times of day at which this policy applies.",
    "addressEntryDisplayTableMSDOS":  "The MAPI display table for an address entry for MSDOS client.",
    "aCSPermissionBits":  "Allows multicast flow origination for a given user.",
    "addressEntryDisplayTable":  "The display table for an address entry.",
    "addressBookRoots2":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "streetAddress":  "Was this page helpful?",
    "aCSMaxSizeOfRSVPAccountFile":  "The maximum size, in bytes, of an RSVP account file.",
    "aCSNonReservedPeakRate":  "The ACS-Non-Reserved-Peak-Rate attribute is for internal use only. Based on RFC2814.",
    "aCSMaxPeakBandwidth":  "The peak bandwidth that can be reserved.",
    "addressBookRoots":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "aCSMaxNoOfAccountFiles":  "The maximum number of RSVP account files.",
    "aCSRSVPLogFilesLocation":  "The file system path for storage of RSVP log files.",
    "aCSNonReservedMinPolicedSize":  "The ACS-Non-Reserved-Min-Policed-Size attribute is for internal use only. Based on RFC2814.",
    "aCSServiceType":  "The ACS service type. Controlled load or guaranteed bandwidth.",
    "aCSDSBMRefresh":  "This attribute contains the interval timer value that determines when the Designated Subnet Bandwidth Manager (DSBM) sends out a refresh message (I_AM_DSBM) to all of the Subnet Bandwidth Managers in a domain.",
    "notes":  "Free text for notes on object.",
    "aCSDSBMDeadTime":  "This attribute contains the election dead time interval (DSBMDeadInterval) for a domain. If the Designated Subnet Bandwidth Manager does not send out an I_AM_DSBM advertisement during this interval, then the other Subnet Bandwidth Managers (SBMs) in the domain elect a new DSBM.",
    "aCSPolicyName":  "String name of an ACS policy that applies to this user.",
    "aCSMinimumDelayVariation":  "The ACS-Minimum-Delay-Variation attribute is for internal use only. Based on RFC2210.",
    "aCSDSBMPriority":  "This attributes contains the priority for this Subnet Bandwidth Manager (SBM). When a new Designated Subnet Bandwidth Manager (DSBM) needs to be elected, this value is sent to other SBMs in the domain as part of a DSBM_willing message. The SBM with the highest priority is elected as the new DSBM.",
    "aCSAllocableRSVPBandwidth":  "The maximum bandwidth that can be reserved."
}
