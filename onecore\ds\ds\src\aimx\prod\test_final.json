{"CommandName": "Add-ADCentralAccessPolicyMember", "Syntax": ["Add-ADCentralAccessPolicyMember\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType ]\n    [-Credential ]\n    [-Identity] \n    [-Members] \n    [-PassThru]\n    [-Server ]\n    []"], "Parameters": [{"Name": "Confirm", "Description": "Prompts you for confirmation before running the cmdlet.", "Details": {"AcceptPipelineInput": "False", "Required": "False", "AcceptWildcardCharacters": "False", "Type": "", "Aliases": "cf", "Default": "False", "Position": "Named"}}, {"Name": "Credential", "Description": "", "Details": {"AcceptPipelineInput": "False", "Required": "False", "AcceptWildcardCharacters": "False", "Type": "", "Aliases": "", "Default": "None", "Position": "Named"}}, {"Name": "Identity", "Description": "", "Details": {"AcceptPipelineInput": "True", "Required": "True", "AcceptWildcardCharacters": "False", "Type": "", "Aliases": "", "Default": "None", "Position": "0"}}, {"Name": "Members", "Description": "Note Name, A distinguished name, GUID (objectGUID)", "Details": {"AcceptPipelineInput": "False", "Required": "True", "AcceptWildcardCharacters": "False", "Type": "", "Aliases": "", "Default": "None", "Position": "1"}}, {"Name": "Server", "Description": "Specify the Active Directory Domain Services instance in one of the following ways: Fully qualified domain name, NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using the Server value from objects passed through the pipeline, By using the domain of the computer running Windows PowerShell", "Details": {"AcceptPipelineInput": "False", "Required": "False", "AcceptWildcardCharacters": "False", "Type": "", "Aliases": "", "Default": "None", "Position": "Named"}}, {"Name": "CommonParameters", "Description": "", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}], "Examples": [{"Code": "$params = @{\n    Identity = 'Finance Policy'\n    Member = 'Finance Documents Rule', 'Corporate Documents Rule'\n}\nAdd-ADCentralAccessPolicyMember @params", "Description": "This command adds the central access rules Finance Documents Rule and Corporate Documents Rule\nto the central access policy Finance Policy."}, {"Code": "Get-ADCentralAccessPolicy -Filter \"Name -like 'Corporate*'\" |\n    Add-ADCentralAccessPolicyMember -Members 'Corporate Documents Rule'", "Description": "This command gets all central access policies that have a name that starts with Corporate and then\npasses this information to Add-ADCentralAccessPolicyMember by using the pipeline operator. The\nAdd-ADCentralAccessPolicyMember cmdlet then adds the central access rule with the name\nCorporate Documents Rule to it."}]}