param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING TYPE EXTRACTION FOR: $CommandName ===" -ForegroundColor Green
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        # Find the first parameter
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)')
        
        if ($paramMatches.Count -gt 0) {
            $firstParam = $paramMatches[0]
            $paramName = $firstParam.Groups[1].Value.Trim()
            $paramContent = $firstParam.Groups[2].Value
            
            Write-Host "`n=== FIRST PARAMETER: $paramName ===" -ForegroundColor Cyan
            
            # Look for Type specifically
            $tableMatches = [regex]::Matches($paramContent, '(?s)<table[^>]*>(.*?)</table>')
            
            foreach ($tableMatch in $tableMatches) {
                $tableContent = $tableMatch.Groups[1].Value
                $rowMatches = [regex]::Matches($tableContent, '(?s)<tr[^>]*>(.*?)</tr>')
                
                foreach ($rowMatch in $rowMatches) {
                    $rowContent = $rowMatch.Groups[1].Value
                    $cellMatches = [regex]::Matches($rowContent, '<td[^>]*>(.*?)</td>')
                    
                    if ($cellMatches.Count -ge 2) {
                        $keyRaw = $cellMatches[0].Groups[1].Value
                        $valueRaw = $cellMatches[1].Groups[1].Value
                        $key = $keyRaw -replace '<[^>]+>', ''
                        $value = $valueRaw -replace '<[^>]+>', ''
                        
                        Write-Host "Raw Key: '$keyRaw'" -ForegroundColor Yellow
                        Write-Host "Clean Key: '$($key.Trim())'" -ForegroundColor Green
                        Write-Host "Raw Value: '$valueRaw'" -ForegroundColor Yellow  
                        Write-Host "Clean Value: '$($value.Trim())'" -ForegroundColor Green
                        Write-Host "---" -ForegroundColor Gray
                        
                        if ($key.Trim() -eq "Type:" -or $key.Trim() -eq "Type") {
                            Write-Host "*** FOUND TYPE FIELD ***" -ForegroundColor Red
                            Write-Host "Type Value: '$($value.Trim())'" -ForegroundColor Red
                        }
                    }
                }
            }
        }
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
