# Debug script to examine the HTML structure for syntax and examples
$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember"

Write-Host "=== DEBUGGING SYNTAX AND EXAMPLES ===" -ForegroundColor Yellow
Write-Host "URL: $URL" -ForegroundColor White

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "HTML content length: $($htmlContent.Length)" -ForegroundColor Green
    
    # Look for syntax section
    Write-Host "`n=== SYNTAX SECTION ===" -ForegroundColor Yellow
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)<h2') {
        $syntaxSection = $matches[1]
        Write-Host "Found Syntax section (length: $($syntaxSection.Length))" -ForegroundColor Green
        
        # Extract code blocks from syntax section
        $codeMatches = [regex]::Matches($syntaxSection, '<code[^>]*>(.*?)</code>')
        Write-Host "Found $($codeMatches.Count) code blocks in syntax section" -ForegroundColor White
        
        foreach ($match in $codeMatches) {
            $codeText = $match.Groups[1].Value
            $codeText = $codeText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            $codeText = $codeText -replace '<[^>]+>', '' # Remove HTML tags
            $codeText = $codeText.Trim()
            
            if ($codeText -and $codeText.Length -gt 10) {
                Write-Host "  Code: $($codeText.Substring(0, [Math]::Min(100, $codeText.Length)))..." -ForegroundColor Cyan
            }
        }
    } else {
        Write-Host "No Syntax section found" -ForegroundColor Red
    }
    
    # Look for examples section
    Write-Host "`n=== EXAMPLES SECTION ===" -ForegroundColor Yellow
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        Write-Host "Found Examples section (length: $($examplesSection.Length))" -ForegroundColor Green
        
        # Find example headers (h3 tags)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>(.*?)</h3>(.*?)(?=<h3|$)')
        Write-Host "Found $($exampleMatches.Count) example sections" -ForegroundColor White
        
        foreach ($match in $exampleMatches) {
            $title = $match.Groups[1].Value -replace '<[^>]+>', '' # Remove HTML tags
            $content = $match.Groups[2].Value
            
            Write-Host "`n  Example: $title" -ForegroundColor Cyan
            
            # Find code in this example
            $codeMatches = [regex]::Matches($content, '<code[^>]*>(.*?)</code>')
            foreach ($codeMatch in $codeMatches) {
                $codeText = $codeMatch.Groups[1].Value
                $codeText = $codeText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                $codeText = $codeText -replace '<[^>]+>', '' # Remove HTML tags
                $codeText = $codeText.Trim()
                
                if ($codeText -and $codeText.Length -gt 10 -and -not ($codeText -match "PS C:\\>")) {
                    Write-Host "    Code: $($codeText.Substring(0, [Math]::Min(80, $codeText.Length)))..." -ForegroundColor White
                }
            }
            
            # Find description text (paragraphs after code)
            $paragraphMatches = [regex]::Matches($content, '<p[^>]*>(.*?)</p>')
            foreach ($pMatch in $paragraphMatches) {
                $pText = $pMatch.Groups[1].Value -replace '<[^>]+>', '' # Remove HTML tags
                $pText = $pText.Trim()
                
                if ($pText -and $pText.Length -gt 20 -and -not ($pText -match "PS C:\\>")) {
                    Write-Host "    Description: $($pText.Substring(0, [Math]::Min(80, $pText.Length)))..." -ForegroundColor Green
                }
            }
        }
    } else {
        Write-Host "No Examples section found" -ForegroundColor Red
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
