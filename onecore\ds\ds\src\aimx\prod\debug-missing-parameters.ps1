param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING MISSING PARAMETERS FOR: $CommandName ===" -ForegroundColor Red
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        Write-Host "`n=== SEARCHING FOR ALL PARAMETER HEADERS ===" -ForegroundColor Yellow
        
        # Find ALL parameter headers (h3 tags)
        $allParamMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>')
        
        Write-Host "Found $($allParamMatches.Count) parameter headers:" -ForegroundColor Cyan
        
        foreach ($match in $allParamMatches) {
            $paramName = $match.Groups[1].Value.Trim()
            Write-Host "  - $paramName" -ForegroundColor White
        }
        
        Write-Host "`n=== CHECKING SPECIFIC MISSING PARAMETERS ===" -ForegroundColor Yellow
        
        # Check for AuthType specifically
        if ($parametersSection -match '(?s)<h3[^>]*>-AuthType</h3>') {
            Write-Host "✅ -AuthType parameter FOUND in HTML" -ForegroundColor Green
        } else {
            Write-Host "❌ -AuthType parameter NOT FOUND in HTML" -ForegroundColor Red
        }
        
        # Check for PassThru specifically  
        if ($parametersSection -match '(?s)<h3[^>]*>-PassThru</h3>') {
            Write-Host "✅ -PassThru parameter FOUND in HTML" -ForegroundColor Green
        } else {
            Write-Host "❌ -PassThru parameter NOT FOUND in HTML" -ForegroundColor Red
        }
        
        Write-Host "`n=== CHECKING FOR COMMON PARAMETERS SECTION ===" -ForegroundColor Yellow
        
        # Check for CommonParameters section
        if ($htmlContent -match '(?s)common parameters') {
            Write-Host "✅ Common parameters text FOUND in HTML" -ForegroundColor Green
            
            # Extract the context around common parameters
            $commonParamMatches = [regex]::Matches($htmlContent, '(?s).{0,200}common parameters.{0,200}')
            foreach ($match in $commonParamMatches) {
                Write-Host "Context: $($match.Value)" -ForegroundColor Gray
                Write-Host "---" -ForegroundColor White
            }
        } else {
            Write-Host "❌ Common parameters text NOT FOUND in HTML" -ForegroundColor Red
        }
        
        Write-Host "`n=== TESTING CURRENT PARAMETER EXTRACTION LOGIC ===" -ForegroundColor Yellow
        
        # Test the current regex that's used in the scraper
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)')
        
        Write-Host "Current regex finds $($paramMatches.Count) parameter sections" -ForegroundColor Cyan
        
        for ($i = 0; $i -lt [Math]::Min(3, $paramMatches.Count); $i++) {
            $match = $paramMatches[$i]
            $paramName = $match.Groups[1].Value.Trim()
            $paramContent = $match.Groups[2].Value
            
            Write-Host "`nParameter $($i+1): $paramName" -ForegroundColor Magenta
            Write-Host "Content length: $($paramContent.Length) chars" -ForegroundColor White
            Write-Host "Content preview: $($paramContent.Substring(0, [Math]::Min(200, $paramContent.Length)))..." -ForegroundColor Gray
        }
        
    } else {
        Write-Host "❌ Parameters section NOT FOUND!" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
