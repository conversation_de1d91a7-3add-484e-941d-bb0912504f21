# Batch script to process all 139 PowerShell Active Directory commands
# with the comprehensive scraper

# Load the original command list
$originalData = Get-Content "netRag/data/ad_powershell_final_rag_with_frequency.json" | ConvertFrom-Json

# Extract command names from the commands property
$commands = $originalData.commands | ForEach-Object { $_.command_name }

Write-Host "Starting comprehensive scraping of $($commands.Count) PowerShell Active Directory commands..." -ForegroundColor Green
Write-Host "Using refined scraper v4 with improved Syntax + Parameters + Example Descriptions (regex-based)" -ForegroundColor Yellow

$allResults = @()
$successCount = 0
$failureCount = 0
$startTime = Get-Date

for ($i = 0; $i -lt $commands.Count; $i++) {
    $command = $commands[$i]
    $progress = [math]::Round(($i + 1) / $commands.Count * 100, 1)
    
    Write-Host "[$($i + 1)/$($commands.Count)] ($progress%) Processing: $command" -ForegroundColor Cyan
    
    # Construct the Microsoft Learn URL
    $url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($command.ToLower())"
    
    try {
        # Run the refined comprehensive scraper v4
        $result = & ".\final-comprehensive-scraper-v4.ps1" -CommandName $command
        
        if ($result) {
            # Parse the JSON result
            $commandData = $result | ConvertFrom-Json
            $allResults += $commandData
            $successCount++
            
            # Show summary info
            $paramCount = if ($commandData.Parameters) { $commandData.Parameters.Count } else { 0 }
            $exampleCount = if ($commandData.Examples) { $commandData.Examples.Count } else { 0 }
            $syntaxCount = if ($commandData.Syntax) { $commandData.Syntax.Count } else { 0 }
            
            Write-Host "  Success - Params: $paramCount, Examples: $exampleCount, Syntax: $syntaxCount" -ForegroundColor Green
        } else {
            Write-Host "  Failed - No result returned" -ForegroundColor Red
            $failureCount++
        }
    }
    catch {
        Write-Host "  Failed - Error: $($_.Exception.Message)" -ForegroundColor Red
        $failureCount++
    }
    
    # Add a small delay to be respectful to Microsoft's servers
    Start-Sleep -Milliseconds 500
}

$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host "`n=== BATCH PROCESSING COMPLETE ===" -ForegroundColor Green
Write-Host "Total commands processed: $($commands.Count)" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failureCount" -ForegroundColor Red
Write-Host "Success rate: $([math]::Round($successCount / $commands.Count * 100, 1))%" -ForegroundColor White
Write-Host "Duration: $($duration.TotalMinutes.ToString('F1')) minutes" -ForegroundColor White

if ($allResults.Count -gt 0) {
    # Save the comprehensive results
    $outputFile = "comprehensive_ad_commands_dataset_v3.json"
    $allResults | ConvertTo-Json -Depth 10 | Out-File -FilePath $outputFile -Encoding UTF8
    
    $fileSize = [math]::Round((Get-Item $outputFile).Length / 1MB, 2)
    Write-Host "`nResults saved to: $outputFile" -ForegroundColor Green
    Write-Host "File size: $fileSize MB" -ForegroundColor White
    
    # Show some statistics
    $totalParams = ($allResults | ForEach-Object { if ($_.Parameters) { $_.Parameters.Count } else { 0 } } | Measure-Object -Sum).Sum
    $totalExamples = ($allResults | ForEach-Object { if ($_.Examples) { $_.Examples.Count } else { 0 } } | Measure-Object -Sum).Sum
    $totalSyntax = ($allResults | ForEach-Object { if ($_.Syntax) { $_.Syntax.Count } else { 0 } } | Measure-Object -Sum).Sum
    
    Write-Host "`n=== DATASET STATISTICS ===" -ForegroundColor Yellow
    Write-Host "Total commands: $($allResults.Count)" -ForegroundColor White
    Write-Host "Total parameters: $totalParams" -ForegroundColor White
    Write-Host "Total examples: $totalExamples" -ForegroundColor White
    Write-Host "Total syntax variations: $totalSyntax" -ForegroundColor White
    Write-Host "Average parameters per command: $([math]::Round($totalParams / $allResults.Count, 1))" -ForegroundColor White
    Write-Host "Average examples per command: $([math]::Round($totalExamples / $allResults.Count, 1))" -ForegroundColor White
} else {
    Write-Host "`nNo results to save!" -ForegroundColor Red
}

Write-Host "`nComprehensive dataset creation complete!" -ForegroundColor Green
