Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DEBUGGING TABLE ORDER FOR FIRST PARAMETER ==="

# Find the first parameter (-AuthType)
$authTypeHeader = $htmlDocument.SelectSingleNode("//h3[contains(text(), '-AuthType')]")
if ($authTypeHeader) {
    Write-Host "Found -AuthType header: $($authTypeHeader.InnerText)"
    
    # Walk through ALL content after this parameter until next parameter
    $nextNode = $authTypeHeader.NextSibling
    $nodeCount = 0
    $tableCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -ne 'h3' -and $nodeCount -lt 50) {
        Write-Host "`nNode $nodeCount : $($nextNode.Name)"
        
        if ($nextNode.Name -eq 'table') {
            $tableCount++
            Write-Host "  === TABLE $tableCount ==="
            $rows = $nextNode.SelectNodes(".//tr")
            if ($rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($cells -and $cells.Count -ge 2) {
                        Write-Host "    $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                    }
                }
            }
        }
        
        if ($nextNode.Name -eq 'p') {
            $text = $nextNode.InnerText.Trim()
            if ($text.Length -gt 0) {
                Write-Host "  P: $($text.Substring(0, [Math]::Min(100, $text.Length)))..."
            }
        }
        
        if ($nextNode.Name -eq 'ul') {
            Write-Host "  UL: List with $($nextNode.SelectNodes('.//li').Count) items"
        }
        
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
    
    Write-Host "`nTotal tables found for -AuthType: $tableCount"
}

# Let's also check the order of ALL tables in the parameters section
Write-Host "`n=== ALL TABLES IN PARAMETERS SECTION (IN ORDER) ==="
$paramSection = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($paramSection) {
    $allTablesInParams = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")
    
    Write-Host "Found $($allTablesInParams.Count) total tables in Parameters section"
    
    for ($i = 0; $i -lt [Math]::Min(10, $allTablesInParams.Count); $i++) {
        Write-Host "`n--- TABLE $($i + 1) ---"
        $table = $allTablesInParams[$i]
        $rows = $table.SelectNodes(".//tr")
        if ($rows) {
            $firstRow = $rows[0]
            $cells = $firstRow.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                Write-Host "  First row: $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
            }
        }
    }
}
