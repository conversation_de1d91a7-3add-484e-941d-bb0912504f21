param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== COMPLETE PARAMETER EXTRACTION DEBUG FOR: $CommandName ===" -ForegroundColor Red
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        Write-Host "`n=== RAW PARAMETERS SECTION ANALYSIS ===" -ForegroundColor Yellow
        Write-Host "Parameters section length: $($parametersSection.Length) characters" -ForegroundColor White
        
        # Show first 1000 chars to see structure
        Write-Host "`nFirst 1000 chars of parameters section:" -ForegroundColor Cyan
        Write-Host $parametersSection.Substring(0, [Math]::Min(1000, $parametersSection.Length)) -ForegroundColor Gray
        
        Write-Host "`n=== FINDING ALL H3 HEADERS (PARAMETERS) ===" -ForegroundColor Yellow
        
        # Find ALL h3 headers in parameters section
        $allH3Matches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>([^<]+)</h3>')
        
        Write-Host "Found $($allH3Matches.Count) H3 headers:" -ForegroundColor Cyan
        
        for ($i = 0; $i -lt $allH3Matches.Count; $i++) {
            $h3Match = $allH3Matches[$i]
            $headerText = $h3Match.Groups[1].Value.Trim()
            Write-Host "  $($i+1). '$headerText'" -ForegroundColor White
        }
        
        Write-Host "`n=== TESTING CURRENT PARAMETER REGEX ===" -ForegroundColor Yellow
        
        # Test the exact regex used in the scraper
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)')
        
        Write-Host "Current regex '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)' finds: $($paramMatches.Count) matches" -ForegroundColor Cyan
        
        for ($i = 0; $i -lt $paramMatches.Count; $i++) {
            $match = $paramMatches[$i]
            $paramName = $match.Groups[1].Value.Trim()
            $paramContent = $match.Groups[2].Value
            
            Write-Host "`n--- Parameter $($i+1): '$paramName' ---" -ForegroundColor Magenta
            Write-Host "Content length: $($paramContent.Length) chars" -ForegroundColor White
            
            # Show first 200 chars of content
            if ($paramContent.Length -gt 0) {
                Write-Host "Content preview: $($paramContent.Substring(0, [Math]::Min(200, $paramContent.Length)))..." -ForegroundColor Gray
            } else {
                Write-Host "⚠️  EMPTY CONTENT!" -ForegroundColor Red
            }
        }
        
        Write-Host "`n=== SEARCHING FOR SPECIFIC MISSING PARAMETERS ===" -ForegroundColor Yellow
        
        # Search for AuthType in the raw HTML
        if ($parametersSection -match '(?s)-AuthType') {
            Write-Host "✅ '-AuthType' text found in parameters section" -ForegroundColor Green
            
            # Find the context around AuthType
            $authTypeMatches = [regex]::Matches($parametersSection, '(?s).{0,100}-AuthType.{0,100}')
            foreach ($match in $authTypeMatches) {
                Write-Host "AuthType context: $($match.Value)" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ '-AuthType' text NOT found in parameters section" -ForegroundColor Red
        }
        
        # Search for PassThru in the raw HTML
        if ($parametersSection -match '(?s)-PassThru') {
            Write-Host "✅ '-PassThru' text found in parameters section" -ForegroundColor Green
            
            # Find the context around PassThru
            $passThruMatches = [regex]::Matches($parametersSection, '(?s).{0,100}-PassThru.{0,100}')
            foreach ($match in $passThruMatches) {
                Write-Host "PassThru context: $($match.Value)" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ '-PassThru' text NOT found in parameters section" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ Parameters section NOT FOUND!" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
