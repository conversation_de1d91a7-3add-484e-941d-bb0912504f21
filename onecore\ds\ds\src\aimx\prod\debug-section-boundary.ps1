param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING PARAMETERS SECTION BOUNDARY ===" -ForegroundColor Red
    
    # Find the Parameters h2 header
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>') {
        $parametersHeaderMatch = $matches[0]
        $parametersHeaderIndex = $htmlContent.IndexOf($parametersHeaderMatch)
        
        Write-Host "✅ Found Parameters header at index: $parametersHeaderIndex" -ForegroundColor Green
        Write-Host "Parameters header: $parametersHeaderMatch" -ForegroundColor Cyan
        
        # Show 2000 characters after the Parameters header
        $afterParameters = $htmlContent.Substring($parametersHeaderIndex, [Math]::Min(2000, $htmlContent.Length - $parametersHeaderIndex))
        
        Write-Host "`n=== FIRST 2000 CHARS AFTER PARAMETERS HEADER ===" -ForegroundColor Yellow
        Write-Host $afterParameters -ForegroundColor Gray
        
        Write-Host "`n=== TESTING CURRENT PARAMETERS SECTION REGEX ===" -ForegroundColor Yellow
        
        # Test current regex
        if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
            $currentParametersSection = $matches[1]
            Write-Host "✅ Current regex captures $($currentParametersSection.Length) characters" -ForegroundColor Green
            
            # Show first 1000 chars of what current regex captures
            Write-Host "`nFirst 1000 chars of current regex capture:" -ForegroundColor Cyan
            Write-Host $currentParametersSection.Substring(0, [Math]::Min(1000, $currentParametersSection.Length)) -ForegroundColor Gray
            
            # Check if AuthType is in the captured section
            if ($currentParametersSection -match '-AuthType') {
                Write-Host "`n✅ -AuthType IS in captured section" -ForegroundColor Green
            } else {
                Write-Host "`n❌ -AuthType NOT in captured section" -ForegroundColor Red
            }
            
            # Check if PassThru is in the captured section
            if ($currentParametersSection -match '-PassThru') {
                Write-Host "✅ -PassThru IS in captured section" -ForegroundColor Green
            } else {
                Write-Host "❌ -PassThru NOT in captured section" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ Current regex FAILED to match" -ForegroundColor Red
        }
        
        Write-Host "`n=== FINDING ALL H3 HEADERS IN FULL HTML ===" -ForegroundColor Yellow
        
        # Find ALL h3 headers in the entire HTML that look like parameters
        $allParamHeaders = [regex]::Matches($htmlContent, '(?s)<h3[^>]*>(-[^<]+)</h3>')
        
        Write-Host "Found $($allParamHeaders.Count) parameter-like H3 headers in full HTML:" -ForegroundColor Cyan
        foreach ($header in $allParamHeaders) {
            $paramName = $header.Groups[1].Value.Trim()
            Write-Host "  - $paramName" -ForegroundColor White
        }
        
    } else {
        Write-Host "❌ Parameters header NOT FOUND!" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
