# Debug description extraction specifically
$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember"

try {
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING EXAMPLE DESCRIPTIONS ===" -ForegroundColor Yellow
    
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        
        # Find each example section (h3 + div pairs)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>(.*?)</h3>\s*<div[^>]*>(.*?)</div>')
        
        Write-Host "Found $($exampleMatches.Count) example sections" -ForegroundColor Green
        
        for ($i = 0; $i -lt $exampleMatches.Count; $i++) {
            $title = $exampleMatches[$i].Groups[1].Value -replace '<[^>]+>', ''
            $exampleContent = $exampleMatches[$i].Groups[2].Value
            
            Write-Host "`n--- Example $($i + 1): $title ---" -ForegroundColor Cyan
            Write-Host "Raw content length: $($exampleContent.Length)" -ForegroundColor White
            
            # Show raw content for debugging
            Write-Host "Raw content (first 500 chars):" -ForegroundColor Yellow
            Write-Host $exampleContent.Substring(0, [Math]::Min(500, $exampleContent.Length)) -ForegroundColor White
            
            # Extract description from p tags
            $descriptionMatches = [regex]::Matches($exampleContent, '(?s)<p[^>]*>(.*?)</p>')
            Write-Host "Found $($descriptionMatches.Count) paragraph(s)" -ForegroundColor Green
            
            foreach ($descMatch in $descriptionMatches) {
                $descText = $descMatch.Groups[1].Value
                Write-Host "  Raw p content: $($descText.Substring(0, [Math]::Min(200, $descText.Length)))..." -ForegroundColor Cyan
                
                # Clean up the text
                $cleanText = $descText -replace '<code[^>]*>(.*?)</code>', '$1' # Keep code content but remove tags
                $cleanText = $cleanText -replace '<[^>]+>', '' # Remove other HTML tags
                $cleanText = $cleanText.Trim()
                
                Write-Host "  Cleaned text: $($cleanText.Substring(0, [Math]::Min(200, $cleanText.Length)))..." -ForegroundColor Green
            }
        }
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
