Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== EXACT TABLE ORDER ANALYSIS ==="

# Get all tables in Parameters section
$allParamTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*//table")
Write-Host "Found $($allParamTables.Count) total tables"

# Get parameter headers
$parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3")
Write-Host "Found $($parameterHeaders.Count) parameter headers"

# Show first few parameter names
for ($i = 0; $i -lt [Math]::Min(5, $parameterHeaders.Count); $i++) {
    $paramName = $parameterHeaders[$i].InnerText.Trim()
    Write-Host "Parameter $($i + 1): $paramName"
}

Write-Host "`n=== TABLE ANALYSIS ==="

# Analyze first 10 tables to understand the pattern
for ($i = 0; $i -lt [Math]::Min(10, $allParamTables.Count); $i++) {
    Write-Host "`n--- TABLE $($i + 1) ---"
    $table = $allParamTables[$i]
    $rows = $table.SelectNodes(".//tr")
    
    if ($rows -and $rows.Count -gt 0) {
        $firstRow = $rows[0]
        $cells = $firstRow.SelectNodes(".//td")
        if ($cells -and $cells.Count -ge 2) {
            $firstKey = $cells[0].InnerText.Trim()
            $firstValue = $cells[1].InnerText.Trim()
            Write-Host "  First row: $firstKey = $firstValue"
            
            # Check if this is a Type table or Parameter Sets table
            if ($firstKey -match "Type") {
                Write-Host "  -> This is a TYPE table"
            } elseif ($firstKey -match "Position") {
                Write-Host "  -> This is a PARAMETER SETS table"
            }
        }
        
        # Show all rows for first few tables
        if ($i -lt 3) {
            foreach ($row in $rows) {
                $cells = $row.SelectNodes(".//td")
                if ($cells -and $cells.Count -ge 2) {
                    Write-Host "    $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                }
            }
        }
    }
}

# Let's also check the pattern by looking for Type tables specifically
Write-Host "`n=== TYPE TABLE POSITIONS ==="
for ($i = 0; $i -lt $allParamTables.Count; $i++) {
    $table = $allParamTables[$i]
    $rows = $table.SelectNodes(".//tr")
    if ($rows) {
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                $key = $cells[0].InnerText.Trim()
                if ($key -match "Type") {
                    Write-Host "Type table found at index $i"
                    break
                }
            }
        }
    }
}
