Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== DEBUGGING PARAMETER DETAILS ==="

# Focus on the first parameter (-AuthType) to understand the structure
$authTypeHeader = $htmlDocument.SelectSingleNode("//h3[text()='-AuthType']")
if ($authTypeHeader) {
    Write-Host "Found -AuthType header"
    
    # Walk through all siblings until next h3 or h2
    $nextNode = $authTypeHeader.NextSibling
    $nodeCount = 0
    
    while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3') -and $nodeCount -lt 20) {
        Write-Host "`nNode $nodeCount : $($nextNode.Name)"
        
        if ($nextNode.Name -eq 'h4') {
            Write-Host "  H4: $($nextNode.InnerText)"
        }
        
        if ($nextNode.Name -eq 'p') {
            Write-Host "  P: $($nextNode.InnerText.Substring(0, [Math]::Min(100, $nextNode.InnerText.Length)))..."
        }
        
        if ($nextNode.Name -eq 'dl') {
            Write-Host "  Found DL!"
            $dtNodes = $nextNode.SelectNodes("./dt")
            if ($dtNodes) {
                foreach ($dt in $dtNodes) {
                    $dd = $dt.SelectSingleNode("./following-sibling::dd[1]")
                    Write-Host "    $($dt.InnerText): $($dd.InnerText)"
                }
            }
        }
        
        if ($nextNode.Name -eq 'table') {
            Write-Host "  Found TABLE!"
            $rows = $nextNode.SelectNodes(".//tr")
            if ($rows) {
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($cells -and $cells.Count -ge 2) {
                        Write-Host "    $($cells[0].InnerText): $($cells[1].InnerText)"
                    }
                }
            }
        }
        
        # Check for nested dl/table structures
        $nestedDl = $nextNode.SelectNodes(".//dl")
        if ($nestedDl) {
            Write-Host "  Found nested DL in $($nextNode.Name)!"
            foreach ($dl in $nestedDl) {
                $dtNodes = $dl.SelectNodes("./dt")
                if ($dtNodes) {
                    foreach ($dt in $dtNodes) {
                        $dd = $dt.SelectSingleNode("./following-sibling::dd[1]")
                        Write-Host "    NESTED: $($dt.InnerText): $($dd.InnerText)"
                    }
                }
            }
        }
        
        $nestedTable = $nextNode.SelectNodes(".//table")
        if ($nestedTable) {
            Write-Host "  Found nested TABLE in $($nextNode.Name)!"
            foreach ($table in $nestedTable) {
                $rows = $table.SelectNodes(".//tr")
                if ($rows) {
                    foreach ($row in $rows) {
                        $cells = $row.SelectNodes(".//td")
                        if ($cells -and $cells.Count -ge 2) {
                            Write-Host "    NESTED: $($cells[0].InnerText): $($cells[1].InnerText)"
                        }
                    }
                }
            }
        }
        
        $nextNode = $nextNode.NextSibling
        $nodeCount++
    }
} else {
    Write-Host "-AuthType header not found"
}

# Also check if there are any tables or dl elements anywhere in the parameters section
Write-Host "`n=== SEARCHING FOR ALL DL/TABLE IN PARAMETERS SECTION ==="
$paramSection = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($paramSection) {
    # Get all content between Parameters h2 and next major h2
    $allDl = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//dl")
    Write-Host "Found $($allDl.Count) DL elements in Parameters section"
    
    $allTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")
    Write-Host "Found $($allTables.Count) TABLE elements in Parameters section"
    
    if ($allDl.Count -gt 0) {
        Write-Host "First DL content:"
        $firstDl = $allDl[0]
        $dtNodes = $firstDl.SelectNodes("./dt")
        if ($dtNodes) {
            foreach ($dt in $dtNodes) {
                $dd = $dt.SelectSingleNode("./following-sibling::dd[1]")
                Write-Host "  $($dt.InnerText): $($dd.InnerText)"
            }
        }
    }
    
    if ($allTables.Count -gt 0) {
        Write-Host "First TABLE content:"
        $firstTable = $allTables[0]
        $rows = $firstTable.SelectNodes(".//tr")
        if ($rows) {
            foreach ($row in $rows) {
                $cells = $row.SelectNodes(".//td")
                if ($cells -and $cells.Count -ge 2) {
                    Write-Host "  $($cells[0].InnerText): $($cells[1].InnerText)"
                }
            }
        }
    }
}
