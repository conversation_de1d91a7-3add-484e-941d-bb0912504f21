﻿{
    "aCSAggregateTokenRatePerUser":  "The maximum token rate any user may have for all flows.",
    "accountExpires":  "The date when the account expires. This value represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of 0 or 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicates that the account never expires. Accounts configured to never expire may have either value, depending on whether they were originally configured with an expiration value, with 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicating that the account has not been previously configured to expire.",
    "accountNameHistory":  "The length of time that the account has been active."
}
