Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== FINDING WHERE TYPE TABLES ARE LOCATED ==="

# Get all tables with Type information
$allTables = $htmlDocument.SelectNodes("//table")
$typeTableIndexes = @()

for ($i = 0; $i -lt $allTables.Count; $i++) {
    $table = $allTables[$i]
    $hasType = $false
    $rows = $table.SelectNodes(".//tr")
    if ($rows) {
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                $key = $cells[0].InnerText.Trim()
                if ($key -match "Type") {
                    $hasType = $true
                    break
                }
            }
        }
    }
    
    if ($hasType) {
        $typeTableIndexes += $i
        Write-Host "`nTYPE TABLE at index $i :"
        foreach ($row in $rows) {
            $cells = $row.SelectNodes(".//td")
            if ($cells -and $cells.Count -ge 2) {
                Write-Host "  $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
            }
        }
        
        # Find what comes before this table
        Write-Host "  CONTEXT: Looking for preceding elements..."
        $currentNode = $table.PreviousSibling
        $stepBack = 0
        while ($null -ne $currentNode -and $stepBack -lt 10) {
            if ($currentNode.Name -eq 'h3') {
                Write-Host "    Previous H3: $($currentNode.InnerText)"
                break
            }
            if ($currentNode.Name -eq 'h4') {
                Write-Host "    Previous H4: $($currentNode.InnerText)"
                break
            }
            if ($currentNode.Name -eq 'p') {
                $text = $currentNode.InnerText.Trim()
                if ($text.Length -gt 0) {
                    Write-Host "    Previous P: $($text.Substring(0, [Math]::Min(50, $text.Length)))..."
                }
            }
            $currentNode = $currentNode.PreviousSibling
            $stepBack++
        }
    }
}

Write-Host "`nFound Type tables at indexes: $($typeTableIndexes -join ', ')"

# Let's also check if there are H4 headers that might be organizing the tables
Write-Host "`n=== CHECKING FOR H4 HEADERS IN PARAMETERS SECTION ==="
$h4Headers = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//h4")
if ($h4Headers) {
    Write-Host "Found $($h4Headers.Count) H4 headers in Parameters section:"
    foreach ($h4 in $h4Headers) {
        Write-Host "  H4: $($h4.InnerText)"
    }
} else {
    Write-Host "No H4 headers found in Parameters section"
}
