param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== SEARCHING FOR TYPE FIELD IN: $CommandName ===" -ForegroundColor Green
    
    # Search for "Type:" in the entire HTML content
    if ($htmlContent -match '(?s)Type:(.*?)(?:</tr>|</td>)') {
        Write-Host "Found Type: field in HTML!" -ForegroundColor Yellow
        Write-Host "Context: $($matches[0])" -ForegroundColor Gray
    }
    
    # Search for all table rows containing "Type"
    $typeMatches = [regex]::Matches($htmlContent, '(?s)<tr[^>]*>.*?Type:.*?</tr>')
    Write-Host "`nFound $($typeMatches.Count) table rows with 'Type:'" -ForegroundColor Cyan
    
    foreach ($match in $typeMatches) {
        Write-Host "Type row: $($match.Value)" -ForegroundColor Gray
        Write-Host "---" -ForegroundColor White
    }
    
    # Also search for the specific parameter section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        # Search for Type in parameters section
        $paramTypeMatches = [regex]::Matches($parametersSection, '(?s)<tr[^>]*>.*?Type:.*?</tr>')
        Write-Host "`nFound $($paramTypeMatches.Count) Type rows in Parameters section" -ForegroundColor Magenta
        
        foreach ($match in $paramTypeMatches) {
            Write-Host "Param Type row: $($match.Value)" -ForegroundColor Gray
            Write-Host "---" -ForegroundColor White
        }
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
