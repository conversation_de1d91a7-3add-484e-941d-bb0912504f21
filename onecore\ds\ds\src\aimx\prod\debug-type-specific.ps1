param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING TYPE FIELD EXTRACTION ===" -ForegroundColor Green
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        # Find the first parameter (Confirm)
        $paramMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>(.*?)(?=<h3|$)')
        
        if ($paramMatches.Count -gt 0) {
            $firstParam = $paramMatches[0]
            $paramName = $firstParam.Groups[1].Value.Trim()
            $paramContent = $firstParam.Groups[2].Value
            
            Write-Host "`n=== PARAMETER: $paramName ===" -ForegroundColor Cyan
            
            # Test Parameter Properties table extraction
            if ($paramContent -match '(?s)<h4[^>]*>Parameter properties</h4>.*?<table[^>]*>(.*?)</table>') {
                $propertiesTable = $matches[1]
                Write-Host "Found Parameter Properties table!" -ForegroundColor Green
                Write-Host "Table content length: $($propertiesTable.Length)" -ForegroundColor Yellow
                
                $rowMatches = [regex]::Matches($propertiesTable, '(?s)<tr[^>]*>(.*?)</tr>')
                Write-Host "Found $($rowMatches.Count) rows" -ForegroundColor Yellow
                
                for ($i = 0; $i -lt $rowMatches.Count; $i++) {
                    $rowMatch = $rowMatches[$i]
                    $rowContent = $rowMatch.Groups[1].Value
                    Write-Host "Row $($i + 1) content: '$rowContent'" -ForegroundColor Cyan

                    $cellMatches = [regex]::Matches($rowContent, '<td[^>]*>(.*?)</td>')
                    Write-Host "  Cells found: $($cellMatches.Count)" -ForegroundColor White

                    if ($cellMatches.Count -ge 2) {
                        $keyRaw = $cellMatches[0].Groups[1].Value
                        $valueRaw = $cellMatches[1].Groups[1].Value
                        $key = $keyRaw -replace '<[^>]+>', ''
                        $value = $valueRaw -replace '<[^>]+>', ''

                        Write-Host "  Raw Key: '$keyRaw'" -ForegroundColor Magenta
                        Write-Host "  Clean Key: '$($key.Trim())'" -ForegroundColor Green
                        Write-Host "  Raw Value: '$valueRaw'" -ForegroundColor Magenta
                        Write-Host "  Clean Value: '$($value.Trim())'" -ForegroundColor Green

                        if ($key.Trim() -eq "Type:") {
                            Write-Host "  *** FOUND TYPE FIELD! ***" -ForegroundColor Red
                            Write-Host "  Type Value: '$($value.Trim())'" -ForegroundColor Red
                        }
                    }
                    Write-Host "---" -ForegroundColor Gray
                }
            } else {
                Write-Host "Parameter Properties table NOT found!" -ForegroundColor Red
                
                # Show first 500 chars of param content to debug
                Write-Host "First 500 chars of param content:" -ForegroundColor Yellow
                Write-Host $paramContent.Substring(0, [Math]::Min(500, $paramContent.Length)) -ForegroundColor Gray
            }
        }
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
