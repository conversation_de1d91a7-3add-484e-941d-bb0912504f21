# Test JSON conversion with descriptions
$testExamples = @()

$testExamples += [PSCustomObject]@{
    Code = "Test-Command -Parameter Value"
    Description = "This is a test description that should appear in the JSON output."
}

$testExamples += [PSCustomObject]@{
    Code = "Another-Command"
    Description = "Another test description with more content to verify it works properly."
}

Write-Host "Test examples created:" -ForegroundColor Green
foreach ($ex in $testExamples) {
    Write-Host "  Code: $($ex.Code)" -ForegroundColor Cyan
    Write-Host "  Desc: $($ex.Description)" -ForegroundColor Yellow
}

$result = [PSCustomObject]@{
    CommandName = "Test-Command"
    Examples = $testExamples
    ExampleCount = $testExamples.Count
}

Write-Host "`nConverting to JSON..." -ForegroundColor Green
$jsonOutput = $result | ConvertTo-Json -Depth 10
Write-Host $jsonOutput -ForegroundColor White

$jsonOutput | Out-File "test_json_conversion.json" -Encoding UTF8
Write-Host "`nSaved to test_json_conversion.json" -ForegroundColor Green
