﻿{
    "CommandName":  "Add-ADCentralAccessPolicyMember",
    "URL":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember",
    "Syntax":  [
                   "Add-ADCentralAccessPolicyMember\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType ]\n    [-Credential ]\n    [-Identity] \n    [-Members] \n    [-PassThru]\n    [-Server ]\n    []"
               ],
    "SyntaxCount":  1,
    "Examples":  [
                     {
                         "Code":  "$params = @{\n    Identity = \u0027Finance Policy\u0027\n    Member = \u0027Finance Documents Rule\u0027, \u0027Corporate Documents Rule\u0027\n}\nAdd-ADCentralAccessPolicyMember @params",
                         "Description":  "This command adds the central access rules Finance Documents Rule and Corporate Documents Rule\nto the central access policy Finance Policy."
                     },
                     {
                         "Code":  "Get-ADCentralAccessPolicy -Filter \"Name -like \u0027Corporate*\u0027\" |\n    Add-ADCentralAccessPolicyMember -Members \u0027Corporate Documents Rule\u0027",
                         "Description":  "This command gets all central access policies that have a name that starts with Corporate and then\npasses this information to Add-ADCentralAccessPolicyMember by using the pipeline operator. The\nAdd-ADCentralAccessPolicyMember cmdlet then adds the central access rule with the name\nCorporate Documents Rule to it."
                     }
                 ],
    "ExampleCount":  2
}
