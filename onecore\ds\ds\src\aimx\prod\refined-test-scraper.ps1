param(
    [Parameter(Mandatory=$true)]
    [string]$URL
)

Import-Module PowerHTML

try {
    $htmlDocument = ConvertFrom-Html -Uri $URL
    
    # Extract command name
    $commandName = $htmlDocument.SelectSingleNode("//h1").InnerText.Trim()
    Write-Host "Processing: $commandName" -ForegroundColor Green
    
    # --- Extract Syntax (improved approach) ---
    $syntax = @()
    Write-Host "=== SYNTAX DEBUGGING ===" -ForegroundColor Yellow
    
    # Try multiple approaches to find syntax
    $syntaxApproaches = @(
        "//h2[text()='Syntax']/following-sibling::div[.//code]",
        "//h2[text()='Syntax']/following-sibling::*[.//code]",
        "//div[contains(@class, 'code')]//code",
        "//pre//code",
        "//code[contains(text(), '$commandName')]"
    )
    
    foreach ($approach in $syntaxApproaches) {
        Write-Host "Trying approach: $approach" -ForegroundColor Cyan
        $syntaxNodes = $htmlDocument.SelectNodes($approach)
        if ($syntaxNodes) {
            Write-Host "  Found $($syntaxNodes.Count) syntax nodes" -ForegroundColor White
            foreach ($syntaxNode in $syntaxNodes) {
                $syntaxText = $syntaxNode.InnerText.Trim()
                $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                if ($syntaxText -match $commandName.Split('-')[1] -and $syntaxText -match "\[.*\]" -and -not ($syntaxText -match "PS C:\\>")) {
                    Write-Host "    Valid syntax: $($syntaxText.Substring(0, [Math]::Min(50, $syntaxText.Length)))..." -ForegroundColor Green
                    $syntax += $syntaxText
                }
            }
        } else {
            Write-Host "  No nodes found" -ForegroundColor Red
        }
    }
    
    # --- Extract Examples (improved approach) ---
    $examples = @()
    Write-Host "`n=== EXAMPLES DEBUGGING ===" -ForegroundColor Yellow
    
    # Find example sections
    $exampleHeaders = $htmlDocument.SelectNodes("//h2[text()='Examples']/following-sibling::h3")
    Write-Host "Found $($exampleHeaders.Count) example headers" -ForegroundColor White
    
    foreach ($exampleHeader in $exampleHeaders) {
        $title = $exampleHeader.InnerText.Trim()
        Write-Host "Processing example: $title" -ForegroundColor Cyan
        
        # Find the code block for this example
        $codeNode = $exampleHeader.SelectSingleNode("following-sibling::*[.//code][1]//code")
        $code = ""
        if ($codeNode) {
            $code = $codeNode.InnerText.Trim()
            $code = $code -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            Write-Host "  Found code: $($code.Substring(0, [Math]::Min(50, $code.Length)))..." -ForegroundColor White
        }
        
        # Find the description that follows the code block
        $description = ""
        # Look for the next paragraph or div after the code block
        $descriptionNode = $exampleHeader.SelectSingleNode("following-sibling::*[.//code][1]/following-sibling::p[1]")
        if (-not $descriptionNode) {
            $descriptionNode = $exampleHeader.SelectSingleNode("following-sibling::p[1]")
        }
        if (-not $descriptionNode) {
            # Try looking for text in the next sibling elements
            $nextSiblings = $exampleHeader.SelectNodes("following-sibling::*[position() <= 5]")
            foreach ($sibling in $nextSiblings) {
                if ($sibling.InnerText -and $sibling.InnerText.Trim().Length -gt 20 -and -not ($sibling.InnerText -match "PS C:\\>")) {
                    $description = $sibling.InnerText.Trim()
                    Write-Host "  Found description in $($sibling.Name): $($description.Substring(0, [Math]::Min(50, $description.Length)))..." -ForegroundColor White
                    break
                }
            }
        } else {
            $description = $descriptionNode.InnerText.Trim()
            Write-Host "  Found description: $($description.Substring(0, [Math]::Min(50, $description.Length)))..." -ForegroundColor White
        }
        
        $examples += [PSCustomObject]@{
            Code = $code
            Description = $description
        }
    }
    
    # Output result for testing
    $result = [PSCustomObject]@{
        CommandName = $commandName
        SyntaxCount = $syntax.Count
        Syntax = $syntax
        ExampleCount = $examples.Count
        Examples = $examples
    }
    
    $result | ConvertTo-Json -Depth 10
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
