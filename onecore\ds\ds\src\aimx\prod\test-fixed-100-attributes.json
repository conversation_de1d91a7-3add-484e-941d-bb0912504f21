﻿{
    "aCSNonReservedTxSize":  "The token bucket size an application can use before a reservation is in place.",
    "aCSMaxDurationPerFlow":  "The maximum duration, in seconds, of any single flow.",
    "aCSMaximumSDUSize":  "The ACS-Maximum-SDU-Size attribute is for internal use only. Based on RFC2210.",
    "aCSNonReservedTxLimit":  "The maximum bandwidth a user application can transmit before a reservation is in place.",
    "attributeID":  "The unique X.500 OID for identifying an attribute.",
    "canonicalName":  "The name of the object in canonical format. myserver2.fabrikam.com/users/jeffsmith is an example of a distinguished name in canonical format. This is a constructed attribute. The results returned are identical to those returned by the following Active Directory function: DsCrackNames(NULL, DS_NAME_FLAG_SYNTACTICAL_ONLY, DS_FQDN_1779_NAME, DS_CANONICAL_NAME, ...).",
    "bytesPerMinute":  "Printer data transfer rate.",
    "appSchemaVersion":  "This attribute stores the schema version of the class store. It is used to provide correct behavior across schema changes.",
    "bootFile":  "Contains the name of the boot image for UNIX identity management support.",
    "aCSMaxPeakBandwidthPerFlow":  "The peak bandwidth any flow can consume.",
    "aCSNonReservedTokenSize":  "The ACS-Non-Reserved-Token-Size attribute is for internal use only. Based on RFC2814.",
    "aCSEnableRSVPMessageLogging":  "True if RSVP logging is enabled.",
    "aCSEventLogLevel":  "RSVP logging level.",
    "applicationName":  "The name of the application.",
    "attributeTypes":  "A multi-valued property that contains strings that represent each attribute in the schema.",
    "aCSEnableACSService":  "True if ACS service is to be enabled.",
    "aCSMaxPeakBandwidth":  "The peak bandwidth that can be reserved.",
    "addressEntryDisplayTableMSDOS":  "The MAPI display table for an address entry for MSDOS client.",
    "cACertificate":  "Certificates of trusted Certification Authorities.",
    "homePostalAddress":  "A user\u0027s home address.",
    "cAConnect":  "The connection string for binding to a certification authority.",
    "attributeDisplayNames":  "The name to be displayed for this object.",
    "aCSServerList":  "Contains the DNS names of Windows NT servers that are allowed to run ACS.",
    "authorityRevocationList":  "Cross certificate, Certificate Revocation List.",
    "associatedName":  "The associatedName attribute type specifies an entry in the organizational DIT that is associated with a DNS domain.",
    "adminDisplayName":  "The name to be displayed on admin screens.",
    "aCSTotalNoOfFlows":  "The total number of flows that a given user may have.",
    "aCSEnableRSVPAccounting":  "True if RSVP accounting is enabled.",
    "aCSMinimumLatency":  "The ACS-Minimum-Latency attribute is for internal use only. Based on RFC2210.",
    "builtinCreationTime":  "The Builtin-Creation-Time attribute is used to support replication to Windows NT 4.0 domains.",
    "aCSAllocableRSVPBandwidth":  "The maximum bandwidth that can be reserved.",
    "aCSMinimumDelayVariation":  "The ACS-Minimum-Delay-Variation attribute is for internal use only. Based on RFC2210.",
    "aCSMaxTokenRatePerFlow":  "The maximum token rate any single flow may have for a given user.",
    "builtinModifiedCount":  "The Builtin-Modified-Count attribute is used to support replication to Windows NT 4.0 domains.",
    "bridgeheadServerListBL":  "The list of servers that are bridgeheads for replication.",
    "adminCount":  "Indicates that a given object has had its ACLs changed to a more secure value by the system because it was a member of one of the administrative groups (directly or transitively).",
    "assistant":  "The distinguished name of a user\u0027s administrative assistant.",
    "aCSMaxSizeOfRSVPLogFile":  "The maximum size, in bytes, of an RSVP log file.",
    "aCSPriority":  "Relative priority of a flow for this user.",
    "additionalTrustedServiceNames":  "A list of services in the domain that can be trusted. Not used by AD.",
    "aCSMaxNoOfLogFiles":  "The maximum number of RSVP log files.",
    "allowedChildClasses":  "Classes that can be contained by a class.",
    "authenticationOptions":  "The authentication options used in ADSI to bind to directory services objects.",
    "aCSAggregateTokenRatePerUser":  "The maximum token rate any user may have for all flows.",
    "aCSMaxTokenBucketPerFlow":  "The ACS-Max-Token-Bucket-Per-Flow attribute is for internal use only. Based on RFC2210.",
    "attributeSyntax":  "The OID for the syntax for this attribute.",
    "badPasswordTime":  "The last time and date that an attempt to log on to this account was made with a password that is not valid. This value is stored as a large integer that represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of zero means that the last time an incorrect password was used is unknown.",
    "aCSMaxNoOfAccountFiles":  "The maximum number of RSVP account files.",
    "aCSNonReservedMinPolicedSize":  "The ACS-Non-Reserved-Min-Policed-Size attribute is for internal use only. Based on RFC2814.",
    "auxiliaryClass":  "The list of auxiliary classes to be associated with this class.",
    "notes":  "Free text for notes on object.",
    "addressSyntax":  "A grammar for encoding the display table properties as a string.",
    "aCSServiceType":  "The ACS service type. Controlled load or guaranteed bandwidth.",
    "addressType":  "A character string that describes the format of the user\u0027s address. Address types map to address formats. That is, by looking at a recipient\u0027s address type, client applications can determine how to format an address that is appropriate for the recipient.",
    "allowedChildClassesEffective":  "A list of classes that can be modified.",
    "aCSDirection":  "Send, Receive, Send/Receive, or None.",
    "attributeCertificateAttribute":  "A digitally signed or certified identity and set of attributes. Used to bind authorization information to an identity.",
    "bridgeheadTransportList":  "Transports for which this server is a bridgehead.",
    "aCSMaxSizeOfRSVPAccountFile":  "The maximum size, in bytes, of an RSVP account file.",
    "aCSDSBMRefresh":  "This attribute contains the interval timer value that determines when the Designated Subnet Bandwidth Manager (DSBM) sends out a refresh message (I_AM_DSBM) to all of the Subnet Bandwidth Managers in a domain.",
    "auditingPolicy":  "Auditing policy for the local policy.",
    "streetAddress":  "The user\u0027s address.",
    "adminDescription":  "The description displayed on admin screens.",
    "attributeSecurityGUID":  "The GUID to be used to apply security credentials to a set of objects.",
    "birthLocation":  "The location where the user was born.",
    "aCSDSBMPriority":  "This attributes contains the priority for this Subnet Bandwidth Manager (SBM). When a new Designated Subnet Bandwidth Manager (DSBM) needs to be elected, this value is sent to other SBMs in the domain as part of a DSBM_willing message. The SBM with the highest priority is elected as the new DSBM.",
    "aCSTimeOfDay":  "Times of day at which this policy applies.",
    "adminMultiselectPropertyPages":  "This is a multivalued attribute whose values are a number that represents the order in which the pages are added and a GUID of a COM object that implements multi-select property pages for the AD Users and Computers snap-in.",
    "addressBookRoots2":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "aCSCacheTimeout":  "The amount of time before the expiration of ACS objects that are cached by the ACS service.",
    "addressBookRoots":  "Used by Exchange. Exchange configures trees of address book containers to show up in the MAPI address book. This attribute on the Exchange Config object lists the roots of the address book container trees.",
    "aCSRSVPLogFilesLocation":  "The file system path for storage of RSVP log files.",
    "aCSIdentityName":  "This attribute contains the DN of a user or OU and is the identity of a user or a group of users to which this QoS policy applies.",
    "adminContextMenu":  "The order number and GUID of the context menu to be used on administration screens.",
    "aCSMinimumPolicedSize":  "The ACS-Minimum-Policed-Size attribute is for internal use only. Based on RFC2210.",
    "businessCategory":  "Descriptive text on an Organizational Unit.",
    "aCSNonReservedMaxSDUSize":  "The ACS-Non-Reserved-Max-SDU-Size attribute is for internal use only. Based on RFC2814.",
    "aCSMaxAggregatePeakRatePerUser":  "The maximum peak rate any user may have for all flows.",
    "aCSPermissionBits":  "Allows multicast flow origination for a given user.",
    "assocNTAccount":  "The Windows\u0026nbsp;NT account that applies to this object.",
    "aCSNonReservedPeakRate":  "The ACS-Non-Reserved-Peak-Rate attribute is for internal use only. Based on RFC2814.",
    "assetNumber":  "The tracking number for the object.",
    "accountNameHistory":  "The length of time that the account has been active.",
    "aCSDSBMDeadTime":  "This attribute contains the election dead time interval (DSBMDeadInterval) for a domain. If the Designated Subnet Bandwidth Manager does not send out an I_AM_DSBM advertisement during this interval, then the other Subnet Bandwidth Managers (SBMs) in the domain elect a new DSBM.",
    "allowedAttributesEffective":  "A list of attributes that can be modified on the object.",
    "buildingName":  "The name of the building where an organization or organizational unit is based.",
    "badPwdCount":  "The number of times the user tried to log on to the account using an incorrect password. A value of 0 indicates that the value is unknown.",
    "aNR":  "Ambiguous name resolution attribute to be used when choosing between objects.",
    "altSecurityIdentities":  "Contains mappings for X.509 certificates or external Kerberos user accounts to this user for the purpose of authentication.",
    "audio":  "The Audio attribute type allows the storing of sounds in the Directory.",
    "appliesTo":  "Contains the list of object classes that the extended right applies to. In the list, an object class is represented by the schemaIDGUID property for its schemaClass object.",
    "aCSPolicyName":  "String name of an ACS policy that applies to this user.",
    "aCSRSVPAccountFilesLocation":  "The directory location of RSVP account files. Defaults to the \"system32\" subdirectory located in the default system directory.",
    "allowedAttributes":  "Attributes that will be permitted to be assigned to a class.",
    "accountExpires":  "The date when the account expires. This value represents the number of 100-nanosecond intervals since January 1, 1601 (UTC). A value of 0 or 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicates that the account never expires. Accounts configured to never expire may have either value, depending on whether they were originally configured with an expiration value, with 0x7FFFFFFFFFFFFFFF (9223372036854775807) indicating that the account has not been previously configured to expire.",
    "bootParameter":  "Provides data that is needed to start a diskless client.",
    "associatedDomain":  "The associatedDomain attribute type specifies a DNS domain that is associated with an object.",
    "addressEntryDisplayTable":  "The display table for an address entry.",
    "adminPropertyPages":  "The order number and GUID of the property pages for an object to be displayed on Active Directory administration screens. For more information see the document, Extending the User Interface for Directory Objects.",
    "cACertificateDN":  "Full distinguished name from the CA certificate."
}
