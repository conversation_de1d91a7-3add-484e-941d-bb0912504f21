param(
    [Parameter(Mandatory=$true)]
    [string]$URL
)

Import-Module PowerHTML

Write-Host "Testing scraper syntax..."

try {
    $htmlDocument = ConvertFrom-Html -Uri $URL
    Write-Host "HTML loaded successfully"
    
    # Test command name extraction
    $commandName = $htmlDocument.SelectSingleNode("//h1").InnerText.Trim()
    Write-Host "Command name: $commandName"
    
    # Test parameter count
    $parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3[following-sibling::h2[1][text()='Inputs' or text()='Outputs' or text()='Notes' or text()='Related Links']]")
    Write-Host "Found $($parameterHeaders.Count) parameter headers"
    
    # Test table count
    $allParamTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")
    Write-Host "Found $($allParamTables.Count) tables in Parameters section"
    
    Write-Host "Syntax test completed successfully"
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}
