param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content using Invoke-WebRequest for better reliability
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    # Also use PowerHTML for parameter extraction
    Import-Module PowerHTML -ErrorAction Stop
    $htmlDocument = ConvertFrom-Html -Uri $URL
    
    # --- Extract Syntax (improved approach using regex) ---
    $syntax = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)<h2') {
        $syntaxSection = $matches[1]
        
        # Find all pre/code blocks in syntax section
        $syntaxMatches = [regex]::Matches($syntaxSection, '(?s)<pre><code[^>]*>(.*?)</code></pre>')
        foreach ($match in $syntaxMatches) {
            $syntaxText = $match.Groups[1].Value
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            $syntaxText = $syntaxText -replace '<[^>]+>', '' # Remove any remaining HTML tags
            $syntaxText = $syntaxText.Trim()
            
            if ($syntaxText -and $syntaxText.Length -gt 10) {
                $syntax += $syntaxText
            }
        }
    }
    
    # --- Extract Parameters (using existing PowerHTML approach) ---
    $parameters = @()
    $parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//h3")
    
    # Get Type tables and Parameter Sets tables separately
    $allTypeTables = $htmlDocument.SelectNodes("//table[.//td[contains(text(), 'Type')]]")
    $allParamSetsTables = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table")
    
    $paramIndex = 0
    foreach ($paramHeader in $parameterHeaders) {
        $paramName = $paramHeader.InnerText.Trim() -replace '^-', ''
        
        # Get parameter description
        $descriptionNode = $paramHeader.SelectSingleNode("following-sibling::p[1]")
        $description = ""
        if ($descriptionNode) {
            $description = $descriptionNode.InnerText.Trim()
            
            # Also check for UL lists with additional values
            $ulNode = $paramHeader.SelectSingleNode("following-sibling::ul[1]")
            if ($ulNode) {
                $listItems = $ulNode.SelectNodes(".//li")
                $listText = ($listItems | ForEach-Object { $_.InnerText.Trim() }) -join ", "
                if ($listText) {
                    $description += " " + $listText
                }
            }
        }
        
        # Initialize parameter details
        $paramDetails = @{
            Type = ""
            Position = ""
            Default = ""
            Required = ""
            AcceptPipelineInput = ""
            AcceptWildcardCharacters = ""
            Aliases = ""
        }
        
        # Type table: Parameter Properties (Type, Default value, etc.)
        if ($paramIndex -lt $allTypeTables.Count) {
            $typeTable = $allTypeTables[$paramIndex]
            $typeRows = $typeTable.SelectNodes(".//tr")
            foreach ($row in $typeRows) {
                $cells = $row.SelectNodes(".//td")
                if ($cells.Count -ge 2) {
                    $key = $cells[0].InnerText.Trim()
                    $value = $cells[1].InnerText.Trim()
                    
                    switch ($key) {
                        "Type" { $paramDetails.Type = $value }
                        "Position" { $paramDetails.Position = $value }
                        "Default value" { $paramDetails.Default = $value }
                        "Required" { $paramDetails.Required = $value }
                        "Accept pipeline input" { $paramDetails.AcceptPipelineInput = $value }
                        "Accept wildcard characters" { $paramDetails.AcceptWildcardCharacters = $value }
                        "Aliases" { $paramDetails.Aliases = $value }
                    }
                }
            }
        }
        
        # Parameter Sets table: (Position, Mandatory, etc.)
        if ($paramIndex -lt $allParamSetsTables.Count) {
            $paramSetsTable = $allParamSetsTables[$paramIndex]
            $paramSetsRows = $paramSetsTable.SelectNodes(".//tr")
            foreach ($row in $paramSetsRows) {
                $cells = $row.SelectNodes(".//td")
                if ($cells.Count -ge 2) {
                    $key = $cells[0].InnerText.Trim()
                    $value = $cells[1].InnerText.Trim()
                    
                    switch ($key) {
                        "Position" { if (-not $paramDetails.Position) { $paramDetails.Position = $value } }
                        "Mandatory" { if (-not $paramDetails.Required) { $paramDetails.Required = $value } }
                        "Value from pipeline" { if (-not $paramDetails.AcceptPipelineInput) { $paramDetails.AcceptPipelineInput = $value } }
                        "Accept wildcard characters" { if (-not $paramDetails.AcceptWildcardCharacters) { $paramDetails.AcceptWildcardCharacters = $value } }
                    }
                }
            }
        }
        
        $parameters += [PSCustomObject]@{
            Name = $paramName
            Description = $description
            Details = $paramDetails
        }
        
        $paramIndex++
    }
    
    # --- Extract Examples with Descriptions (improved approach using regex) ---
    $examples = @()
    if ($htmlContent -match '(?s)<h2[^>]*>Examples</h2>(.*?)(?:<h2|$)') {
        $examplesSection = $matches[1]
        
        # Find each example section (h3 + div pairs)
        $exampleMatches = [regex]::Matches($examplesSection, '(?s)<h3[^>]*>.*?</h3>\s*<div[^>]*>(.*?)</div>')
        
        foreach ($match in $exampleMatches) {
            $exampleContent = $match.Groups[1].Value
            
            # Extract code from pre/code block
            $code = ""
            if ($exampleContent -match '(?s)<pre><code[^>]*>(.*?)</code></pre>') {
                $code = $matches[1]
                $code = $code -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                $code = $code -replace '<[^>]+>', '' # Remove HTML tags
                $code = $code.Trim()
            }
            
            # Extract description from p tags that follow the code (exclude the code part)
            $description = ""
            # Remove the pre/code block first, then extract p tags
            $contentAfterCode = $exampleContent -replace '(?s)<pre><code[^>]*>.*?</code></pre>', ''
            $descriptionMatches = [regex]::Matches($contentAfterCode, '(?s)<p[^>]*>(.*?)</p>')
            $descriptions = @()
            foreach ($descMatch in $descriptionMatches) {
                $descText = $descMatch.Groups[1].Value
                $descText = $descText -replace '<code[^>]*>(.*?)</code>', '$1' # Keep code content but remove tags
                $descText = $descText -replace '<[^>]+>', '' # Remove other HTML tags
                $descText = $descText.Trim()
                
                if ($descText -and $descText.Length -gt 10) {
                    $descriptions += $descText
                }
            }
            
            if ($descriptions.Count -gt 0) {
                $description = $descriptions -join " "
            }
            
            if ($code) {
                $examples += [PSCustomObject]@{
                    Code = $code
                    Description = $description
                }
            }
        }
    }
    
    # --- Create final result ---
    $result = [PSCustomObject]@{
        CommandName = $CommandName
        Syntax = $syntax
        Parameters = $parameters
        Examples = $examples
    }
    
    # Output as JSON
    $result | ConvertTo-Json -Depth 10
}
catch {
    Write-Error "Error processing $CommandName`: $($_.Exception.Message)"
    # Return empty result on error
    [PSCustomObject]@{
        CommandName = $CommandName
        Syntax = @()
        Parameters = @()
        Examples = @()
        Error = $_.Exception.Message
    } | ConvertTo-Json -Depth 10
}
