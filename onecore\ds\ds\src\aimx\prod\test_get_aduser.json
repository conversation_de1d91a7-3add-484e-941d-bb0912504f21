{"CommandName": "Get-ADUser", "Syntax": ["Get-ADUser\n    -Filter \n    [-AuthType ]\n    [-Credential ]\n    [-Properties ]\n    [-ResultPageSize ]\n    [-ResultSetSize ]\n    [-SearchBase ]\n    [-SearchScope ]\n    [-Server ]\n    []", "Get-ADUser\n    [-Identity] \n    [-AuthType ]\n    [-Credential ]\n    [-Partition ]\n    [-Properties ]\n    [-Server ]\n    []", "Get-ADUser\n    -LDAPFilter \n    [-AuthType ]\n    [-Credential ]\n    [-Properties ]\n    [-ResultPageSize ]\n    [-ResultSetSize ]\n    [-SearchBase ]\n    [-SearchScope ]\n    [-Server ]\n    []"], "Parameters": [{"Name": "Credential", "Description": "If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error.", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "Filter", "Description": "Syntax:", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "Identity", "Description": "This parameter can also get this object through the pipeline or you can set this parameter to an object instance. A distinguished name, A GUID (objectGUID), A security identifier (objectSid), A SAM account name (sAMAccountName)", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "LDAPFilter", "Description": "", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "Partition", "Description": "In AD DS environments, a default value for Partition is set in the following cases: If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name., If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive., If none of the previous cases apply, the default value of Partition is set to the default partition or naming context of the target domain. If the Identity parameter is set to a distinguished name, the default value of Partition is automatically generated from this distinguished name., If running cmdlets from an Active Directory provider drive, the default value of Partition is automatically generated from the current path in the drive., If none of the previous cases apply, the Partition parameter does not take any default value.", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "Properties", "Description": "To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the Get-Member cmdlet.", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "Server", "Description": "Domain name values: Fully qualified domain name (FQDN), NetBIOS name Fully qualified directory server name, NetBIOS name, Fully qualified directory server name and port By using Server value from objects passed through the pipeline., By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive., By using the domain of the computer running PowerShell.", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}, {"Name": "CommonParameters", "Description": "", "Details": {"Position": "", "Default": "", "AcceptPipelineInput": "", "AcceptWildcardCharacters": "", "Type": "", "Required": "", "Aliases": ""}}], "Examples": [{"Code": "PS C:\\> Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"", "Description": "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM."}, {"Code": "PS C:\\> Get-ADUser -Filter 'Name -like \"*SvcAccount\"' | Format-Table Name,SamAccountName -A", "Description": "This command gets all users that have a name that ends with SvcAccount."}, {"Code": "PS C:\\> Get-ADUser -Identity ChewDavid -Properties *", "Description": "This command gets all of the properties of the user with the SAM account name ChewDavid."}, {"Code": "PS C:\\> Get-ADUser -Filter \"Name -eq 'ChewDavid'\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000", "Description": "This command gets the user with the name <PERSON><PERSON><PERSON><PERSON><PERSON> in the Active Directory Lightweight Directory Services (AD LDS) instance."}, {"Code": "C:\\PS> Get-ADUser -LDAPFilter '(!userAccountControl:1.2.840.113556.1.4.803:=2)'", "Description": "This command gets all enabled user accounts in Active Directory using an LDAP filter."}]}