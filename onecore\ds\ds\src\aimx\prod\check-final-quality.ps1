# Check the quality of the final comprehensive dataset

$data = Get-Content 'comprehensive_ad_commands_dataset_v3.json' | ConvertFrom-Json

Write-Host "=== FINAL DATASET QUALITY CHECK ===" -ForegroundColor Green

# Check overall structure
Write-Host "`nDataset Structure:" -ForegroundColor Yellow
Write-Host "Total Commands: $($data.Count)" -ForegroundColor White

# Check a specific command (Add-ADCentralAccessPolicyMember)
$testCommand = $data | Where-Object {$_.CommandName -eq 'Add-ADCentralAccessPolicyMember'}

if ($testCommand) {
    Write-Host "`n=== SAMPLE COMMAND: Add-ADCentralAccessPolicyMember ===" -ForegroundColor Cyan
    Write-Host "Syntax blocks: $($testCommand.Syntax.Count)" -ForegroundColor White
    Write-Host "Parameters: $($testCommand.Parameters.Count)" -ForegroundColor White
    Write-Host "Examples: $($testCommand.Examples.Count)" -ForegroundColor White
    
    # Check first parameter details
    if ($testCommand.Parameters.Count -gt 0) {
        $firstParam = $testCommand.Parameters[0]
        Write-Host "`nFirst Parameter: $($firstParam.Name)" -ForegroundColor Magenta
        Write-Host "  Type: '$($firstParam.Details.Type)'" -ForegroundColor White
        Write-Host "  Required: '$($firstParam.Details.Required)'" -ForegroundColor White
        Write-Host "  Position: '$($firstParam.Details.Position)'" -ForegroundColor White
        Write-Host "  Default: '$($firstParam.Details.Default)'" -ForegroundColor White
        Write-Host "  AcceptPipelineInput: '$($firstParam.Details.AcceptPipelineInput)'" -ForegroundColor White
        Write-Host "  AcceptWildcardCharacters: '$($firstParam.Details.AcceptWildcardCharacters)'" -ForegroundColor White
        Write-Host "  Aliases: '$($firstParam.Details.Aliases)'" -ForegroundColor White
    }
    
    # Check first example
    if ($testCommand.Examples.Count -gt 0) {
        $firstExample = $testCommand.Examples[0]
        Write-Host "`nFirst Example:" -ForegroundColor Magenta
        Write-Host "  Code length: $($firstExample.Code.Length) chars" -ForegroundColor White
        Write-Host "  Description length: $($firstExample.Description.Length) chars" -ForegroundColor White
        Write-Host "  Code preview: $($firstExample.Code.Substring(0, [Math]::Min(50, $firstExample.Code.Length)))..." -ForegroundColor Gray
    }
}

# Check parameter completeness across all commands
Write-Host "`n=== PARAMETER COMPLETENESS ANALYSIS ===" -ForegroundColor Yellow

$totalParams = 0
$paramsWithType = 0
$paramsWithPosition = 0
$paramsWithRequired = 0

foreach ($command in $data) {
    foreach ($param in $command.Parameters) {
        $totalParams++
        if ($param.Details.Type -and $param.Details.Type.Trim() -ne "") { $paramsWithType++ }
        if ($param.Details.Position -and $param.Details.Position.Trim() -ne "") { $paramsWithPosition++ }
        if ($param.Details.Required -and $param.Details.Required.Trim() -ne "") { $paramsWithRequired++ }
    }
}

Write-Host "Total Parameters: $totalParams" -ForegroundColor White
Write-Host "Parameters with Type: $paramsWithType ($([math]::Round($paramsWithType/$totalParams*100,1))%)" -ForegroundColor White
Write-Host "Parameters with Position: $paramsWithPosition ($([math]::Round($paramsWithPosition/$totalParams*100,1))%)" -ForegroundColor White
Write-Host "Parameters with Required: $paramsWithRequired ($([math]::Round($paramsWithRequired/$totalParams*100,1))%)" -ForegroundColor White

Write-Host "`n=== QUALITY CHECK COMPLETE ===" -ForegroundColor Green
