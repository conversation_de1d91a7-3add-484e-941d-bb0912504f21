<#
.SYNOPSIS
    Enhanced PowerShell command scraper for Microsoft Learn documentation.

.DESCRIPTION
    This script extracts comprehensive information from Microsoft Learn PowerShell documentation pages,
    including Synopsis, Parameters with full details, and Examples with actual code.

.PARAMETER URL
    The URL of the Microsoft Learn page to scrape.

.EXAMPLE
    .\enhanced-scraper.ps1 -URL "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
#>
param (
    [Parameter(Mandatory = $true)]
    [string]$URL
)

try {
    Import-Module PowerHTML -ErrorAction Stop
}
catch {
    Write-Error "The 'PowerHTML' module is not installed. Please run: Install-Module -Name PowerHTML"
    return
}

try {
    $htmlDocument = ConvertFrom-Html -Uri $URL -ErrorAction Stop
}
catch {
    Write-Error "Failed to fetch or parse the URL: $URL. Please check the URL and your internet connection."
    return
}

# Extract command name from h1
$commandName = $htmlDocument.SelectSingleNode("//h1").InnerText.Trim()

# --- Extract Synopsis ---
$synopsis = "Synopsis not found."
# Try multiple possible locations for synopsis/description
$descriptionHeader = $htmlDocument.SelectSingleNode("//h2[text()='Description']")
if ($null -ne $descriptionHeader) {
    $nextNode = $descriptionHeader.NextSibling
    while ($null -ne $nextNode -and $nextNode.Name -ne 'h2') {
        if ($nextNode.Name -eq 'p') {
            $synopsis = $nextNode.InnerText.Trim()
            break
        }
        $nextNode = $nextNode.NextSibling
    }
}

# If Description section not found, try looking for summary or first paragraph
if ($synopsis -eq "Synopsis not found.") {
    # Try to find the first meaningful paragraph after the h1
    $h1Node = $htmlDocument.SelectSingleNode("//h1")
    if ($null -ne $h1Node) {
        $nextNode = $h1Node.NextSibling
        while ($null -ne $nextNode -and $nextNode.Name -ne 'h2') {
            if ($nextNode.Name -eq 'p' -and $nextNode.InnerText.Trim().Length -gt 20) {
                $synopsis = $nextNode.InnerText.Trim()
                break
            }
            $nextNode = $nextNode.NextSibling
        }
    }
}

# --- Extract Parameters ---
$parameters = @()
$parametersHeader = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($null -ne $parametersHeader) {
    # Find all h3 parameter headers that come after Parameters h2
    $parameterHeaders = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::h3[following-sibling::h2[1][text()='Inputs' or text()='Outputs' or text()='Notes' or text()='Related Links']]")
    
    foreach ($paramHeader in $parameterHeaders) {
        $paramName = $paramHeader.InnerText.Trim()
        
        # Skip CommonParameters
        if ($paramName -eq "CommonParameters") { continue }
        
        $description = ""
        $details = @{}
        
        # Get content between this h3 and the next h3 or h2
        $nextNode = $paramHeader.NextSibling
        $contentNodes = @()
        
        while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3')) {
            $contentNodes += $nextNode
            $nextNode = $nextNode.NextSibling
        }
        
        # Extract description and details
        foreach ($node in $contentNodes) {
            if ($node.Name -eq 'p') {
                if ([string]::IsNullOrEmpty($description)) {
                    $description = $node.InnerText.Trim()
                }
            }
            
            # Look for definition lists (dl/dt/dd) containing parameter details
            $dlNodes = $node.SelectNodes(".//dl")
            if ($null -ne $dlNodes) {
                foreach ($dl in $dlNodes) {
                    $dtNodes = $dl.SelectNodes("./dt")
                    if ($null -ne $dtNodes) {
                        foreach ($dt in $dtNodes) {
                            $key = $dt.InnerText.Trim()
                            $ddNode = $dt.SelectSingleNode("./following-sibling::dd[1]")
                            if ($null -ne $ddNode) {
                                $value = $ddNode.InnerText.Trim()
                                $details[$key] = $value
                            }
                        }
                    }
                }
            }
        }
        
        $parameters += [PSCustomObject]@{
            Name        = $paramName
            Description = $description
            Details     = $details
        }
    }
}

# --- Extract Examples ---
$examples = @()
$examplesHeader = $htmlDocument.SelectSingleNode("//h2[text()='Examples']")
if ($null -ne $examplesHeader) {
    # Find all h3 example headers that come after Examples h2 but before Parameters h2
    $exampleHeaders = $htmlDocument.SelectNodes("//h2[text()='Examples']/following-sibling::h3[following-sibling::h2[1][text()='Parameters']]")

    foreach ($exampleHeader in $exampleHeaders) {
        $title = $exampleHeader.InnerText.Trim()

        # Skip if this looks like a parameter (starts with -)
        if ($title -match "^-\w+") { continue }

        $description = ""
        $code = ""

        # Get content between this h3 and the next h3 or h2
        $nextNode = $exampleHeader.NextSibling
        $contentNodes = @()

        while ($null -ne $nextNode -and $nextNode.Name -notin @('h2', 'h3')) {
            $contentNodes += $nextNode
            $nextNode = $nextNode.NextSibling
        }

        # Extract description and code
        foreach ($node in $contentNodes) {
            if ($node.Name -eq 'p') {
                $description += $node.InnerText.Trim() + " "
            }

            # Look for code blocks - try multiple selectors
            $codeNodes = $node.SelectNodes(".//code")
            if ($null -ne $codeNodes) {
                foreach ($codeNode in $codeNodes) {
                    $codeText = $codeNode.InnerText.Trim()
                    # Clean up HTML entities
                    $codeText = $codeText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                    # Only capture PowerShell commands (starting with PS C:\> or containing cmdlet patterns)
                    if ($codeText -match "PS C:\\>" -or $codeText -match "Get-|Set-|New-|Remove-|Add-") {
                        $code += $codeText + "`n"
                    }
                }
            }

            # Also check for pre tags
            $preNodes = $node.SelectNodes(".//pre")
            if ($null -ne $preNodes) {
                foreach ($preNode in $preNodes) {
                    $preText = $preNode.InnerText.Trim()
                    $preText = $preText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
                    if ($preText -match "PS C:\\>" -or $preText -match "Get-|Set-|New-|Remove-|Add-") {
                        $code += $preText + "`n"
                    }
                }
            }
        }

        $examples += [PSCustomObject]@{
            Title       = $title
            Description = $description.Trim()
            Code        = $code.Trim()
        }
    }
}

# --- Combine all information ---
$commandInfo = [PSCustomObject]@{
    CommandName = $commandName
    Synopsis   = $synopsis
    Parameters = $parameters
    Examples   = $examples
}

# --- Output as JSON ---
$commandInfo | ConvertTo-Json -Depth 5 | Write-Output
