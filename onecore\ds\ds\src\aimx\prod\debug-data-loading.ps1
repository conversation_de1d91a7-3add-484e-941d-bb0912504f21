# Debug script to check data loading
Write-Host "=== DEBUGGING DATA LOADING ===" -ForegroundColor Yellow

# Check if file exists
$filePath = "netRag/data/ad_powershell_final_rag_with_frequency.json"
if (Test-Path $filePath) {
    Write-Host "File exists: $filePath" -ForegroundColor Green
    
    # Get file size
    $fileSize = (Get-Item $filePath).Length
    Write-Host "File size: $fileSize bytes" -ForegroundColor White
    
    try {
        # Load the data
        $originalData = Get-Content $filePath | ConvertFrom-Json
        Write-Host "Data loaded successfully" -ForegroundColor Green

        # Check data type
        $dataType = $originalData.GetType().Name
        Write-Host "Data type: $dataType" -ForegroundColor White

        # Check if it's an array or object
        if ($originalData -is [Array]) {
            Write-Host "Data is an array with $($originalData.Count) items" -ForegroundColor White

            if ($originalData.Count -gt 0) {
                # Extract command names
                $commands = $originalData | ForEach-Object { $_.command_name }
                Write-Host "Commands extracted: $($commands.Count)" -ForegroundColor White

                # Show first 5 command names
                Write-Host "First 5 command names:" -ForegroundColor Cyan
                for ($i = 0; $i -lt [Math]::Min(5, $commands.Count); $i++) {
                    Write-Host "  $($i + 1). $($commands[$i])" -ForegroundColor White
                }
            } else {
                Write-Host "Array is empty!" -ForegroundColor Red
            }
        } else {
            Write-Host "Data is an object, not an array" -ForegroundColor Yellow
            Write-Host "Object properties:" -ForegroundColor Cyan
            $originalData | Get-Member -MemberType NoteProperty | ForEach-Object {
                Write-Host "  $($_.Name)" -ForegroundColor White
            }

            # Check if there's a property that contains the array
            if ($originalData.PSObject.Properties.Name -contains "commands") {
                Write-Host "Found 'commands' property" -ForegroundColor Green
                $commands = $originalData.commands | ForEach-Object { $_.command_name }
                Write-Host "Commands in 'commands' property: $($commands.Count)" -ForegroundColor White
            }
        }
    }
    catch {
        Write-Host "Error loading data: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "File does not exist: $filePath" -ForegroundColor Red
    
    # List files in the directory
    Write-Host "Files in netRag/data directory:" -ForegroundColor Yellow
    Get-ChildItem "netRag/data" | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
}
