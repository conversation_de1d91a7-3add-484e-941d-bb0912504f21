param(
    [Parameter(Mandatory=$true)]
    [string]$CommandName
)

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/$($CommandName.ToLower())"

try {
    # Get the HTML content
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    Write-Host "=== DEBUGGING H3 REGEX ISSUE ===" -ForegroundColor Red
    
    # Extract the Parameters section
    if ($htmlContent -match '(?s)<h2[^>]*>Parameters</h2>(.*?)(?:<h2[^>]*>(?:Inputs|Outputs)|$)') {
        $parametersSection = $matches[1]
        
        Write-Host "✅ Parameters section captured: $($parametersSection.Length) chars" -ForegroundColor Green
        
        Write-Host "`n=== TESTING DIFFERENT H3 REGEX PATTERNS ===" -ForegroundColor Yellow
        
        # Test 1: Current regex used in scraper
        Write-Host "`n1. Current scraper regex: '(?s)<h3[^>]*[^>]*>([^<]+)</h3>'" -ForegroundColor Cyan
        $currentMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*[^>]*>([^<]+)</h3>')
        Write-Host "   Found $($currentMatches.Count) matches:" -ForegroundColor White
        foreach ($match in $currentMatches) {
            Write-Host "     - '$($match.Groups[1].Value.Trim())'" -ForegroundColor Gray
        }
        
        # Test 2: Simpler H3 regex
        Write-Host "`n2. Simpler H3 regex: '(?s)<h3[^>]*>([^<]+)</h3>'" -ForegroundColor Cyan
        $simpleMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>([^<]+)</h3>')
        Write-Host "   Found $($simpleMatches.Count) matches:" -ForegroundColor White
        foreach ($match in $simpleMatches) {
            Write-Host "     - '$($match.Groups[1].Value.Trim())'" -ForegroundColor Gray
        }
        
        # Test 3: Look for any H3 with parameter-like content
        Write-Host "`n3. H3 with dash content: '(?s)<h3[^>]*>([^<]*-[^<]*)</h3>'" -ForegroundColor Cyan
        $dashMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>([^<]*-[^<]*)</h3>')
        Write-Host "   Found $($dashMatches.Count) matches:" -ForegroundColor White
        foreach ($match in $dashMatches) {
            Write-Host "     - '$($match.Groups[1].Value.Trim())'" -ForegroundColor Gray
        }
        
        # Test 4: Look for specific AuthType and PassThru
        Write-Host "`n4. Searching for specific parameters in raw HTML:" -ForegroundColor Cyan
        
        # Search for AuthType H3
        $authTypeMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>.*?AuthType.*?</h3>')
        Write-Host "   AuthType H3 matches: $($authTypeMatches.Count)" -ForegroundColor White
        foreach ($match in $authTypeMatches) {
            Write-Host "     AuthType H3: $($match.Value)" -ForegroundColor Gray
        }
        
        # Search for PassThru H3
        $passThruMatches = [regex]::Matches($parametersSection, '(?s)<h3[^>]*>.*?PassThru.*?</h3>')
        Write-Host "   PassThru H3 matches: $($passThruMatches.Count)" -ForegroundColor White
        foreach ($match in $passThruMatches) {
            Write-Host "     PassThru H3: $($match.Value)" -ForegroundColor Gray
        }
        
        Write-Host "`n=== EXAMINING FIRST 2000 CHARS FOR H3 STRUCTURE ===" -ForegroundColor Yellow
        $first2000 = $parametersSection.Substring(0, [Math]::Min(2000, $parametersSection.Length))
        
        # Find all H3 tags in first 2000 chars
        $h3Tags = [regex]::Matches($first2000, '(?s)<h3[^>]*>.*?</h3>')
        Write-Host "H3 tags in first 2000 chars: $($h3Tags.Count)" -ForegroundColor White
        foreach ($tag in $h3Tags) {
            Write-Host "  H3 tag: $($tag.Value)" -ForegroundColor Gray
        }
        
    } else {
        Write-Host "❌ Parameters section NOT FOUND!" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
