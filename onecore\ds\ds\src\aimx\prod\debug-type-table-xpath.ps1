Import-Module PowerHTML

$url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adtrust"
$htmlDocument = ConvertFrom-Html -Uri $url

Write-Host "=== FINDING TYPE TABLES WITH DIFFERENT XPATH ==="

# Try different XPath expressions to find Type tables
$xpathExpressions = @(
    "//table[.//td[contains(text(), 'Type')]]",
    "//h2[text()='Parameters']//table[.//td[contains(text(), 'Type')]]",
    "//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]//table[.//td[contains(text(), 'Type')]]",
    "//table[.//td[text()='Type::']]",
    "//div//table[.//td[contains(text(), 'Type')]]"
)

foreach ($xpath in $xpathExpressions) {
    Write-Host "`nTrying XPath: $xpath"
    $typeTables = $htmlDocument.SelectNodes($xpath)
    if ($typeTables) {
        Write-Host "  Found $($typeTables.Count) Type tables"
        
        # Show first table details
        if ($typeTables.Count -gt 0) {
            $firstTable = $typeTables[0]
            $rows = $firstTable.SelectNodes(".//tr")
            if ($rows) {
                Write-Host "  First Type table content:"
                foreach ($row in $rows) {
                    $cells = $row.SelectNodes(".//td")
                    if ($cells -and $cells.Count -ge 2) {
                        Write-Host "    $($cells[0].InnerText.Trim()): $($cells[1].InnerText.Trim())"
                    }
                }
            }
        }
    } else {
        Write-Host "  No tables found"
    }
}

# Let's also check the document structure around parameters more carefully
Write-Host "`n=== DOCUMENT STRUCTURE ANALYSIS ==="
$paramSection = $htmlDocument.SelectSingleNode("//h2[text()='Parameters']")
if ($paramSection) {
    Write-Host "Found Parameters section"
    
    # Get all elements between Parameters and next major section
    $allElements = $htmlDocument.SelectNodes("//h2[text()='Parameters']/following-sibling::*[following-sibling::h2[1][text()='Inputs' or text()='Outputs']]")
    Write-Host "Found $($allElements.Count) elements between Parameters and next section"
    
    $tableCount = 0
    $h3Count = 0
    $h4Count = 0
    
    foreach ($element in $allElements) {
        if ($element.Name -eq 'table') {
            $tableCount++
        } elseif ($element.Name -eq 'h3') {
            $h3Count++
        } elseif ($element.Name -eq 'h4') {
            $h4Count++
        }
    }
    
    Write-Host "  Tables: $tableCount"
    Write-Host "  H3 headers: $h3Count"
    Write-Host "  H4 headers: $h4Count"
}

# Check if Type tables are in a different section
Write-Host "`n=== CHECKING ALL SECTIONS FOR TYPE TABLES ==="
$allSections = $htmlDocument.SelectNodes("//h2")
foreach ($section in $allSections) {
    $sectionName = $section.InnerText.Trim()
    $typeTables = $htmlDocument.SelectNodes("//h2[text()='$sectionName']/following-sibling::*//table[.//td[contains(text(), 'Type')]]")
    if ($typeTables -and $typeTables.Count -gt 0) {
        Write-Host "Found $($typeTables.Count) Type tables in section: $sectionName"
    }
}
