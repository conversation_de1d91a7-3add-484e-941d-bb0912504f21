# Check what parameters actually exist in PowerShell help vs documentation

Write-Host "=== CHECKING POWERSHELL HELP FOR Add-ADCentralAccessPolicyMember ===" -ForegroundColor Green

try {
    # Try to get PowerShell help (if AD module is available)
    $helpInfo = Get-Help Add-ADCentralAccessPolicyMember -Full -ErrorAction SilentlyContinue
    
    if ($helpInfo) {
        Write-Host "`n✅ PowerShell Help Available" -ForegroundColor Green
        
        if ($helpInfo.parameters) {
            Write-Host "`nParameters from PowerShell Help:" -ForegroundColor Yellow
            foreach ($param in $helpInfo.parameters.parameter) {
                Write-Host "  - $($param.name)" -ForegroundColor White
            }
        }
        
        if ($helpInfo.syntax) {
            Write-Host "`nSyntax from PowerShell Help:" -ForegroundColor Yellow
            foreach ($syntaxItem in $helpInfo.syntax.syntaxItem) {
                Write-Host "Syntax: $($syntaxItem.name)" -ForegroundColor Cyan
                foreach ($param in $syntaxItem.parameter) {
                    $required = if ($param.required -eq "true") { "[Required]" } else { "[Optional]" }
                    Write-Host "  $required -$($param.name)" -ForegroundColor White
                }
                Write-Host ""
            }
        }
    } else {
        Write-Host "❌ PowerShell Help Not Available (AD module not loaded)" -ForegroundColor Red
        Write-Host "This is expected in this environment" -ForegroundColor Gray
    }
}
catch {
    Write-Host "❌ Error accessing PowerShell Help: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== CHECKING SYNTAX SECTION IN DOCUMENTATION ===" -ForegroundColor Yellow

$URL = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/add-adcentralaccesspolicymember"

try {
    $response = Invoke-WebRequest -Uri $URL -UseBasicParsing
    $htmlContent = $response.Content
    
    # Extract syntax section
    if ($htmlContent -match '(?s)<h2[^>]*>Syntax</h2>(.*?)(?:<h2[^>]*>|$)') {
        $syntaxSection = $matches[1]
        
        Write-Host "✅ Syntax section found" -ForegroundColor Green
        
        # Extract all syntax blocks
        $syntaxMatches = [regex]::Matches($syntaxSection, '(?s)<pre><code[^>]*>(.*?)</code></pre>')
        
        Write-Host "`nSyntax blocks from documentation:" -ForegroundColor Cyan
        foreach ($match in $syntaxMatches) {
            $syntaxText = $match.Groups[1].Value
            $syntaxText = $syntaxText -replace '&gt;', '>' -replace '&lt;', '<' -replace '&amp;', '&'
            $syntaxText = $syntaxText -replace '<[^>]+>', ''
            $syntaxText = $syntaxText.Trim()
            
            Write-Host "---" -ForegroundColor Gray
            Write-Host $syntaxText -ForegroundColor White
            Write-Host "---" -ForegroundColor Gray
            
            # Extract parameter names from syntax
            $paramMatches = [regex]::Matches($syntaxText, '-(\w+)')
            Write-Host "Parameters found in this syntax:" -ForegroundColor Yellow
            foreach ($paramMatch in $paramMatches) {
                Write-Host "  - $($paramMatch.Groups[1].Value)" -ForegroundColor White
            }
            Write-Host ""
        }
    } else {
        Write-Host "❌ Syntax section not found" -ForegroundColor Red
    }
}
catch {
    Write-Error "Error: $($_.Exception.Message)"
}
